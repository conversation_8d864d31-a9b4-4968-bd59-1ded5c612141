//
//  AudioOperations.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import Foundation
import SwiftUI
import AVKit
import UniformTypeIdentifiers
import PhotosUI
import AVFoundation
import Speech

func splitAudioIntoChunks(audioURL: URL, chunkDuration: TimeInterval = 10, completion: @escaping ([URL]) -> Void) {
    let asset = AVURLAsset(url: audioURL)
    
    Task {
        do {
            let duration = try await asset.load(.duration)
            let totalDuration = CMTimeGetSeconds(duration)
            var chunkURLs: [URL] = []
            let fileManager = FileManager.default

            for startTime in stride(from: 0, to: totalDuration, by: chunkDuration) {
                let chunkEndTime = min(startTime + chunkDuration, totalDuration)
                let outputURL = fileManager.temporaryDirectory.appendingPathComponent("chunk_\(Int(startTime))_\(UUID().uuidString).m4a")

                // Delete file if exists
                if fileManager.fileExists(atPath: outputURL.path) {
                    try? fileManager.removeItem(at: outputURL)
                }

                do {
                    let exportedURL = try await exportAudio(asset: asset, outputURL: outputURL, startTime: startTime, chunkEndTime: chunkEndTime)
                    chunkURLs.append(exportedURL)
                } catch {
                    print("❌ Export failed: \(error.localizedDescription)")
                }
            }
            completion(chunkURLs)
        } catch {
            print("❌ Failed to load duration: \(error.localizedDescription)")
            completion([])
        }
    }
}

func exportAudio(asset: AVAsset, outputURL: URL, startTime: TimeInterval, chunkEndTime: TimeInterval) async throws -> URL {
    guard let exporter = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetAppleM4A) else {
        throw NSError(domain: "ExportSessionError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Failed to create AVAssetExportSession"])
    }

    exporter.outputURL = outputURL
    exporter.outputFileType = .m4a
    exporter.timeRange = CMTimeRange(start: CMTime(seconds: startTime, preferredTimescale: 600),
                                     end: CMTime(seconds: chunkEndTime, preferredTimescale: 600))

    return try await withCheckedThrowingContinuation { continuation in
        exporter.exportAsynchronously {
            switch exporter.status {
            case .completed:
                continuation.resume(returning: outputURL)
            case .failed:
                continuation.resume(throwing: exporter.error ?? NSError(domain: "ExportFailed", code: 2, userInfo: nil))
            case .cancelled:
                continuation.resume(throwing: NSError(domain: "ExportCancelled", code: 3, userInfo: nil))
            default:
                continuation.resume(throwing: NSError(domain: "UnknownExportStatus", code: 4, userInfo: nil))
            }
        }
    }
}

func transcribeAudio(audioURL: URL, chunkStartTime: TimeInterval, completion: @escaping ([(TimeInterval, TimeInterval, String)]) -> Void) {
    let recognizer = SFSpeechRecognizer()
    let request = SFSpeechURLRecognitionRequest(url: audioURL)
    
    if let recognizer = SFSpeechRecognizer(), recognizer.supportsOnDeviceRecognition {
        request.requiresOnDeviceRecognition = true
    }
    
    

    recognizer?.recognitionTask(with: request) { result, error in
        if let result = result, result.isFinal {
            var sentences: [(TimeInterval, TimeInterval, String)] = []
            var currentSentence = ""
            var sentenceStartTime: TimeInterval? = nil
            var lastWordTime: TimeInterval = 0

            for segment in result.bestTranscription.segments {
                let word = segment.substring
                let timestamp = segment.timestamp + chunkStartTime

                if sentenceStartTime == nil {
                    sentenceStartTime = timestamp
                }

                currentSentence.append(currentSentence.isEmpty ? word : " \(word)")
                lastWordTime = timestamp

                // Consider ending a sentence when encountering common delimiters
                if word.hasSuffix(".") || word.hasSuffix("?") || word.hasSuffix("!") {
                    if let startTime = sentenceStartTime {
                        sentences.append((startTime, lastWordTime, currentSentence))
                        currentSentence = ""
                        sentenceStartTime = nil
                    }
                }
            }

            // If any words remain unprocessed as a final sentence
            if !currentSentence.isEmpty, let startTime = sentenceStartTime {
                sentences.append((startTime, lastWordTime, currentSentence))
            }

            DispatchQueue.main.async {
                completion(sentences)
            }
        } else if let error = error {
            DispatchQueue.main.async {
                if error.localizedDescription.contains("Siri and Dictation are disabled") {
                    let alert = NSAlert()
                    alert.messageText = "Speech Recognition Disabled"
                    alert.informativeText = """
                    ❗️Speech recognition requires Dictation or Siri to be enabled on your Mac.

                    ➤ Enable Dictation:
                    System Settings → Keyboard → Dictation → Enable Dictation

                    ➤ Or enable Siri:
                    System Settings → Siri → Enable Ask Siri

                    ⚠️ IMPORTANT: After enabling, you MUST restart this app for speech recognition to work.
                    """
                    alert.alertStyle = .warning
                    alert.addButton(withTitle: "Open Keyboard Settings")
                    alert.addButton(withTitle: "Open Siri Settings")
                    alert.addButton(withTitle: "Cancel")

                    let response = alert.runModal()
                    switch response {
                    case .alertFirstButtonReturn:
                        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.keyboard?Keyboard") {
                            NSWorkspace.shared.open(url)
                        }
                    case .alertSecondButtonReturn:
                        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.siri") {
                            NSWorkspace.shared.open(url)
                        }
                    default:
                        break
                    }
                    
                } 
                completion([])
            }
        }
    }
}

func exportTranscript(_ transcript: [(TimeInterval, TimeInterval, String)]) {
    let panel = NSSavePanel()
    
    panel.allowedContentTypes = [UTType.plainText]

    panel.nameFieldStringValue = "Transcript.txt"
    panel.canCreateDirectories = true
    panel.title = "Save Transcript"

    panel.begin { response in
        if response == .OK, let url = panel.url {
            let formattedText = transcript.map { item in
                let start = formatTimestamp(item.0)
                let end = formatTimestamp(item.1)
                return "[\(start) - \(end)] \(item.2)"
            }.joined(separator: "\n\n")

            do {
                try formattedText.write(to: url, atomically: true, encoding: .utf8)
                print("✅ Transcript saved to \(url)")
            } catch {
                print("❌ Failed to save transcript: \(error.localizedDescription)")
            }
        }
    }
}

func extractChunkNumber(from filename: String) -> Int {
    let pattern = "chunk_(\\d+)_"
    if let regex = try? NSRegularExpression(pattern: pattern),
       let match = regex.firstMatch(in: filename, range: NSRange(filename.startIndex..., in: filename)) {
        if let range = Range(match.range(at: 1), in: filename),
           let number = Int(filename[range]) {
            return number
        }
    }
    return Int.max // Default to large value in case of failure
}

func transcribeChunksAndSaveSync(chunkURLs: [URL], completion: @escaping ([(TimeInterval, TimeInterval, String)]) -> Void) {
    let sortedChunks = chunkURLs.sorted { extractChunkNumber(from: $0.lastPathComponent) < extractChunkNumber(from: $1.lastPathComponent) }
    
    var fullTranscript: [(TimeInterval, TimeInterval, String)] = []
    var chunkIndex = 0
    
    func processNextChunk() {
        guard chunkIndex < sortedChunks.count else {
            DispatchQueue.main.async {
                completion(fullTranscript)
            }
            return
        }
        
        let chunkURL = sortedChunks[chunkIndex]
        let chunkStartTime = TimeInterval(extractChunkNumber(from: chunkURL.lastPathComponent))
        
        transcribeAudio(audioURL: chunkURL, chunkStartTime: chunkStartTime) { transcript in
            fullTranscript.append(contentsOf: transcript)
            chunkIndex += 1
            processNextChunk()
        }
    }
    
    processNextChunk()
}



class AudioTranscriptionManager: ObservableObject {
    @Published var isConvertingToAudio: Bool = false
    @Published var isTranscribing: Bool = false
    @Published var audioFileURL: URL?
    @Published var showAudioSection: Bool = false
    @Published var showTranscriptSection: Bool = false
    @Published var transcriptText: [(TimeInterval, TimeInterval, String)] = [(0.0000, 0.0000, "")]
    @Published var errorTitle: String?
    @Published var errorMessage: String?
    @Published var showErrorAlert: Bool = false
    
    func extractAudio(from videoURL: URL, completion: (() -> Void)? = nil) {
        self.isConvertingToAudio = true
        let asset = AVAsset(url: videoURL)
        print("Stage 1")

        Task {
            do {
                // Load audio tracks asynchronously
                let audioTracks = try await asset.loadTracks(withMediaType: .audio)
                guard !audioTracks.isEmpty else {
                    DispatchQueue.main.async {
                        self.isConvertingToAudio = false
                        self.errorTitle = "No audio output"
                        self.errorMessage = "This video has no audio track to extract."
                        self.showErrorAlert = true
                        completion?()
                    }
                    return
                }

                let fileManager = FileManager.default
                let outputURL = fileManager.temporaryDirectory.appendingPathComponent("extractedAudio.m4a")

                if fileManager.fileExists(atPath: outputURL.path) {
                    do {
                        try fileManager.removeItem(at: outputURL)
                        print("🗑️ Existing file deleted: \(outputURL.path)")
                    } catch {
                        DispatchQueue.main.async {
                            self.isConvertingToAudio = false
                            self.errorMessage = "Failed to delete old audio file."
                            completion?()
                        }
                        return
                    }
                }

                guard let exporter = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetAppleM4A) else {
                    DispatchQueue.main.async {
                        self.isConvertingToAudio = false
                        self.errorMessage = "Failed to create audio exporter."
                        completion?()
                    }
                    return
                }
  print("Stage 2")
                exporter.outputURL = outputURL
                exporter.outputFileType = .m4a

                exporter.exportAsynchronously {
                    DispatchQueue.main.async {
                        self.isConvertingToAudio = false
                        switch exporter.status {
                        case .completed:
                            print("Passed stage")
                            self.audioFileURL = outputURL
                            self.showAudioSection = true
                            completion?()
                            print( self.showAudioSection)
                        case .failed, .cancelled:
                            print("FAILED STAGE")
                            self.errorMessage = "Audio export failed: \(exporter.error?.localizedDescription ?? "Unknown error")"
                            completion?()
                        default:
                            self.errorMessage = "Unexpected export status: \(exporter.status.rawValue)"
                            completion?()
                        }
                    }
                }

            } catch {
                DispatchQueue.main.async {
                    self.errorTitle = "Audio Extraction Failed"
                    self.isConvertingToAudio = false
                    self.errorMessage = "Failed to load audio tracks: \(error.localizedDescription)"
                    completion?()
                }
            }
        }
    }
    
    func transcribeVideo(from videoURL: URL, completion: (() -> Void)? = nil) {
        checkAndRequestSpeechPermission { [weak self] granted in
            guard let self = self, granted else {
                completion?()
                return
            }
            
            if let audioURL = self.audioFileURL {
                self.isTranscribing = true
                splitAudioIntoChunks(audioURL: audioURL) { chunkURLs in
                    self.transcribeChunksAndSaveSync(chunkURLs: chunkURLs) {
                        completion?()
                    }
                }
            } else {
                self.extractAudio(from: videoURL) {
                    if let audioURL = self.audioFileURL {
                        self.isTranscribing = true
                        splitAudioIntoChunks(audioURL: audioURL) { chunkURLs in
                            self.transcribeChunksAndSaveSync(chunkURLs: chunkURLs) {
                                completion?()
                            }
                        }
                    } else {
                        DispatchQueue.main.async {
                            self.errorMessage = "Audio extraction failed. Please try again."
                            self.showErrorAlert = true
                            completion?()
                        }
                    }
                }
            }
        }
    }
    
    func transcribeChunksAndSaveSync(chunkURLs: [URL], completion: (() -> Void)? = nil) {
        let sortedChunks = chunkURLs.sorted { extractChunkNumber(from: $0.lastPathComponent) < extractChunkNumber(from: $1.lastPathComponent) }
        var fullTranscript: [(TimeInterval, TimeInterval, String)] = []
        var chunkIndex = 0

        func processNextChunk() {
            guard chunkIndex < sortedChunks.count else {
                DispatchQueue.main.async {
                    self.transcriptText = fullTranscript
                    self.isTranscribing = false
                    self.showTranscriptSection = true
                    completion?()
                }
                return
            }

            let chunkURL = sortedChunks[chunkIndex]
            let chunkStartTime = TimeInterval(extractChunkNumber(from: chunkURL.lastPathComponent))

            transcribeAudio(audioURL: chunkURL, chunkStartTime: chunkStartTime) { transcript in
                fullTranscript.append(contentsOf: transcript)
                chunkIndex += 1
                processNextChunk()
            }
        }
        
        processNextChunk()
    }
    
    func enableAIEnhancement(for videoURL: URL, completion: @escaping (Bool) -> Void) {
        // If transcript is already available, just complete successfully
        if showTranscriptSection && !transcriptText.isEmpty {
            DispatchQueue.main.async {
                completion(true)
            }
            return
        }
        
        // Reuse the existing transcribeVideo function with a completion handler
        transcribeVideo(from: videoURL) {
            DispatchQueue.main.async {
                completion(self.showTranscriptSection)
            }
        }
    }
    
    func reset() {
        audioFileURL = nil
        isTranscribing = false
        isConvertingToAudio = false
        showAudioSection = false
        showTranscriptSection = false
        transcriptText = [(0.0000, 0.0000, "")]
    }
}
