//
//  ScriptWriterManager.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import Foundation
import SwiftUI
import UniformTypeIdentifiers
import SwiftData
import AppKit

enum ScriptType: String, CaseIterable {
    case educational = "Educational"
    case entertainment = "Entertainment"
    case tutorial = "Tutorial"
    case review = "Review"
    case vlog = "Vlog"
    case news = "News/Commentary"
    case gaming = "Gaming"
    case cooking = "Cooking"
    case fitness = "Fitness"
    case tech = "Technology"
    case business = "Business"
    case lifestyle = "Lifestyle"
    
    var icon: String {
        switch self {
        case .educational: return "graduationcap"
        case .entertainment: return "theatermasks"
        case .tutorial: return "play.rectangle"
        case .review: return "star.bubble"
        case .vlog: return "person.wave.2"
        case .news: return "newspaper"
        case .gaming: return "gamecontroller"
        case .cooking: return "fork.knife"
        case .fitness: return "figure.run"
        case .tech: return "laptopcomputer"
        case .business: return "briefcase"
        case .lifestyle: return "heart"
        }
    }
    
    var description: String {
        switch self {
        case .educational: return "Informative content that teaches concepts"
        case .entertainment: return "Fun, engaging content for entertainment"
        case .tutorial: return "Step-by-step how-to guides"
        case .review: return "Product or service reviews"
        case .vlog: return "Personal vlogs and daily life content"
        case .news: return "News analysis and commentary"
        case .gaming: return "Gaming content and walkthroughs"
        case .cooking: return "Recipes and cooking tutorials"
        case .fitness: return "Workout routines and health tips"
        case .tech: return "Technology reviews and tutorials"
        case .business: return "Business advice and entrepreneurship"
        case .lifestyle: return "Lifestyle tips and personal development"
        }
    }
}

enum ScriptLength: String, CaseIterable {
    case short = "Short (1-3 minutes)"
    case medium = "Medium (5-10 minutes)"
    case long = "Long (15-20 minutes)"
    case extended = "Extended (30+ minutes)"
    
    var estimatedWords: String {
        switch self {
        case .short: return "150-450 words"
        case .medium: return "750-1500 words"
        case .long: return "2250-3000 words"
        case .extended: return "4500+ words"
        }
    }
    
    var targetDuration: String {
        switch self {
        case .short: return "1-3 minutes"
        case .medium: return "5-10 minutes"
        case .long: return "15-20 minutes"
        case .extended: return "30+ minutes"
        }
    }
}

enum ScriptTone: String, CaseIterable {
    case professional = "Professional"
    case casual = "Casual & Friendly"
    case energetic = "Energetic & Enthusiastic"
    case educational = "Educational & Informative"
    case humorous = "Humorous & Light"
    case serious = "Serious & Authoritative"
    case conversational = "Conversational"
    
    var description: String {
        switch self {
        case .professional: return "Formal, business-like tone"
        case .casual: return "Relaxed, friendly conversation"
        case .energetic: return "High energy, exciting delivery"
        case .educational: return "Clear, instructional approach"
        case .humorous: return "Light-hearted with humor"
        case .serious: return "Authoritative and serious"
        case .conversational: return "Natural, like talking to a friend"
        }
    }
}

class ScriptWriterManager: NSObject, ObservableObject {
    @Published var isGenerating: Bool = false
    @Published var generatedScript: String = ""
    @Published var errorMessage: String?
    @Published var showErrorAlert: Bool = false
    @Published var generationProgress: Double = 0.0
    @Published var currentStep: String = ""
    @Published var isCopied: Bool = false
    // Script parameters
    @Published var topic: String = ""
    @Published var selectedType: ScriptType = .educational
    @Published var selectedLength: ScriptLength = .medium
    @Published var selectedTone: ScriptTone = .conversational
    @Published var targetAudience: String = ""
    @Published var keyPoints: String = ""
    @Published var includeHook: Bool = true
   // @Published var includeCTA: Bool = true
    @Published var customInstructions: String = ""

    private let localAIService = LocalAIService.shared
    private let geminiViewModel = GeminiViewModel()

    // Recent Activity integration
    private var activityManager: RecentActivityManager?
    private var modelContext: ModelContext?
    
    override init() {
        super.init()
        self.activityManager = RecentActivityManager()
    }

    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
        self.activityManager?.setModelContext(context)
    }
    
    func generateScript(selectedTool: ToolOption? = nil) {
        guard !topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            errorMessage = "Please enter a topic for your script"
            showErrorAlert = true
            return
        }

        Task {
            await generateScriptWithAI(selectedTool: selectedTool)
        }
    }
    
    @MainActor
    private func generateScriptWithAI(selectedTool: ToolOption? = nil) async {
        isGenerating = true
        generationProgress = 0.0
        currentStep = "Preparing script generation..."
        errorMessage = nil
        generatedScript = ""

        var webSearchData: String = ""

        // Check if web search tool is selected
        if selectedTool == .searchWeb {
            currentStep = "Fetching recent data from web search..."
            generationProgress = 0.1

            // Call Gemini web search
            await geminiViewModel.fetchGeminiSearchResult(prompt: topic)

            webSearchData = geminiViewModel.geminiResponse

            // Check if web search was successful
            if webSearchData.contains("[ERROR]") || webSearchData.contains("Could not find") {
                currentStep = "Web search failed, continuing with local knowledge..."
                webSearchData = "" // Clear error data, continue without web search
            } else {
                currentStep = "Web search completed, generating enhanced script..."
            }

            generationProgress = 0.2
        }

        let prompt = createScriptPrompt(webSearchData: webSearchData)

        currentStep = "Generating script with AI..."
        generationProgress = 0.3

        await localAIService.send(prompt: prompt)

        generationProgress = 0.8
        currentStep = "Finalizing script..."

        let rawScript = localAIService.response
        let cleanedScript = cleanAndFormatScript(rawScript)

        if !cleanedScript.isEmpty {
            generatedScript = cleanedScript
            generationProgress = 1.0
            currentStep = "Script generated successfully!"

            // Automatically save to Recent Activity
            saveScriptToRecentActivity(script: cleanedScript)
        } else {
            errorMessage = "Failed to generate script. Please try again."
            showErrorAlert = true
        }

        isGenerating = false
    }
    
    private func createScriptPrompt(webSearchData: String = "") -> String {
        let audienceInfo = targetAudience.isEmpty ? "general audience" : targetAudience
        let keyPointsInfo = keyPoints.isEmpty ? "" : "\n\nKey points to cover:\n\(keyPoints)"
        let customInfo = customInstructions.isEmpty ? "" : "\n\nAdditional instructions:\n\(customInstructions)"

        // Add web search data if available
        let webSearchInfo = webSearchData.isEmpty ? "" : """

        **Recent Web Search Data:**
        Use the following recent information as source material to enhance your script with current, up-to-date content:

        \(webSearchData)

        Please incorporate relevant information from this data into your script to make it more current and informative.
        """

        return """
        Create a YouTube video script with the following specifications:

        **Topic**: \(topic)
        **Video Type**: \(selectedType.rawValue) - \(selectedType.description)
        **Length**: \(selectedLength.rawValue) (\(selectedLength.estimatedWords))
        **Tone**: \(selectedTone.rawValue) - \(selectedTone.description)
        **Target Audience**: \(audienceInfo)
        **Include Hook**: \(includeHook ? "Yes" : "No")\(webSearchInfo)\(keyPointsInfo)\(customInfo)

        **Script Requirements:**
        1. Start with an engaging hook (if requested) that grabs attention in the first 15 seconds
        2. Structure the content logically with clear sections
        3. Use the specified tone throughout
        4. Include natural transitions between sections
        5. Add timing cues and delivery notes in [brackets]
        6. End with a strong call-to-action (if requested)
        7. Target approximately \(selectedLength.estimatedWords) for \(selectedLength.targetDuration) of content
        8. If web search data is provided, incorporate the most relevant and recent information naturally into the script

        **Format the script as:**

        [HOOK - 0:00-0:15]
        [Opening content here]

        [MAIN CONTENT - 0:15-X:XX]
        [Section 1]
        [Content here]

        [Section 2]
        [Content here]

        [CONCLUSION - X:XX-End]
        [Wrap up and CTA here]


        Create an engaging, well-structured script that matches the specified criteria. Include delivery notes and timing suggestions throughout.
        """
    }
    
    private func cleanAndFormatScript(_ rawScript: String) -> String {
        // Remove any AI response prefixes
        var cleaned = rawScript
            .replacingOccurrences(of: "Here's your script:", with: "")
            .replacingOccurrences(of: "Here is the script:", with: "")
            .replacingOccurrences(of: "Script:", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Ensure proper formatting
        if !cleaned.contains("[HOOK") && includeHook {
            cleaned = "[HOOK - 0:00-0:15]\n\n" + cleaned
        }
        
        return cleaned
    }
    
    func reset() {
        topic = ""
        selectedType = .educational
        selectedLength = .medium
        selectedTone = .conversational
        targetAudience = ""
        keyPoints = ""
        includeHook = true
        customInstructions = ""
        generatedScript = ""
        errorMessage = nil
        showErrorAlert = false
        generationProgress = 0.0
        currentStep = ""
        isGenerating = false
    }
    
    func exportScript() {
        let panel = NSSavePanel()

        panel.allowedContentTypes = [UTType.plainText]
        panel.nameFieldStringValue = "Script_\(topic.replacingOccurrences(of: " ", with: "_")).txt"
        panel.canCreateDirectories = true
        panel.title = "Save Script"

        panel.begin { response in
            if response == .OK, let url = panel.url {
                let header = """
                YouTube Video Script
                Generated on: \(Date().formatted(date: .abbreviated, time: .shortened))

                Topic: \(self.topic)
                Type: \(self.selectedType.rawValue)
                Length: \(self.selectedLength.rawValue)
                Tone: \(self.selectedTone.rawValue)
                Target Audience: \(self.targetAudience.isEmpty ? "General" : self.targetAudience)

                ═══════════════════════════════════════════════════════════════

                """

                let fullScript = header + self.generatedScript

                do {
                    try fullScript.write(to: url, atomically: true, encoding: .utf8)
                    print("✅ Script saved to \(url)")

                    // Open Finder and highlight the saved file
                    NSWorkspace.shared.activateFileViewerSelecting([url])
                } catch {
                    print("❌ Failed to save script: \(error.localizedDescription)")
                    DispatchQueue.main.async {
                        self.errorMessage = "Failed to save script: \(error.localizedDescription)"
                        self.showErrorAlert = true
                    }
                }
            }
        }
    }

    func copyScriptToClipboard() {
        let header = """
        YouTube Video Script
        Generated on: \(Date().formatted(date: .abbreviated, time: .shortened))

        Topic: \(topic)
        Type: \(selectedType.rawValue)
        Length: \(selectedLength.rawValue)
        Tone: \(selectedTone.rawValue)
        Target Audience: \(targetAudience.isEmpty ? "General" : targetAudience)

        ═══════════════════════════════════════════════════════════════

        """

        let fullScript = header + generatedScript

        NSPasteboard.general.clearContents()
        NSPasteboard.general.setString(fullScript, forType: .string)
        isCopied = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.isCopied = false
        }
    }

    // MARK: - Recent Activity Integration

    /// Automatically saves generated script to Recent Activity
    private func saveScriptToRecentActivity(script: String) {
        guard let activityManager = activityManager else {
            print("❌ Activity manager not available")
            return
        }

        // Generate title based on topic and type
        let title = topic.isEmpty ? "Generated Script" : "Script: \(topic)"

        // Create metadata
        let metadata: [String: String] = [
            "topic": topic,
            "type": selectedType.rawValue,
            "length": selectedLength.rawValue,
            "tone": selectedTone.rawValue,
            "targetAudience": targetAudience.isEmpty ? "General" : targetAudience,
            "includeHook": includeHook ? "Yes" : "No",
            "source": "AIScriptWriter"
        ]

        // Save to Recent Activity
        activityManager.saveScriptContent(
            title: title,
            content: script,
            metadata: metadata
        )
    }
}
