//
//  RecentActivityModels.swift
//  YoutubeUploaderVer1
//
//  Created by Augment Agent on 24/06/25.
//

import Foundation
import SwiftData

// MARK: - Content Type Enum
enum ContentType: String, CaseIterable, Codable {
    case video = "video"
    case script = "script"
    
    var displayName: String {
        switch self {
        case .video:
            return "Video Clip"
        case .script:
            return "AI Script"
        }
    }
    
    var iconName: String {
        switch self {
        case .video:
            return "video.fill"
        case .script:
            return "doc.text.fill"
        }
    }
}

// MARK: - Recent Activity Item Model
@Model
final class RecentActivityItem {
    var id: UUID
    var title: String
    var contentType: ContentType
    var createdDate: Date
    var filePath: String?
    var fileSize: Int64
    var duration: Double? // For videos
    var wordCount: Int? // For scripts
    var thumbnail: Data? // For video thumbnails
    var content: String? // For script content
    var metadata: [String: String] // Additional metadata
    
    init(
        title: String,
        contentType: ContentType,
        filePath: String? = nil,
        fileSize: Int64 = 0,
        duration: Double? = nil,
        wordCount: Int? = nil,
        thumbnail: Data? = nil,
        content: String? = nil,
        metadata: [String: String] = [:]
    ) {
        self.id = UUID()
        self.title = title
        self.contentType = contentType
        self.createdDate = Date()
        self.filePath = filePath
        self.fileSize = fileSize
        self.duration = duration
        self.wordCount = wordCount
        self.thumbnail = thumbnail
        self.content = content
        self.metadata = metadata
    }
    
    // MARK: - Computed Properties
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdDate)
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
    
    var formattedDuration: String? {
        guard let duration = duration else { return nil }
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    var isExpired: Bool {
        let expirationDate = Calendar.current.date(byAdding: .day, value: 7, to: createdDate) ?? createdDate
        return Date() > expirationDate
    }
    
    var daysUntilExpiration: Int {
        let expirationDate = Calendar.current.date(byAdding: .day, value: 7, to: createdDate) ?? createdDate
        let days = Calendar.current.dateComponents([.day], from: Date(), to: expirationDate).day ?? 0
        return max(0, days)
    }
    
    // MARK: - File Management
    
    var fileExists: Bool {
        guard let filePath = filePath else { return false }
        return FileManager.default.fileExists(atPath: filePath)
    }
    
    var fileURL: URL? {
        guard let filePath = filePath else { return nil }
        return URL(fileURLWithPath: filePath)
    }
    
    func deleteFile() {
        guard let filePath = filePath,
              FileManager.default.fileExists(atPath: filePath) else { return }
        
        do {
            try FileManager.default.removeItem(atPath: filePath)
            print("✅ Deleted file: \(filePath)")
        } catch {
            print("❌ Failed to delete file: \(error.localizedDescription)")
        }
    }
}

// MARK: - Recent Activity Manager
@Observable
class RecentActivityManager {
    private var modelContext: ModelContext?
    
    init(modelContext: ModelContext? = nil) {
        self.modelContext = modelContext
    }
    
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
    }
    
    // MARK: - Content Management
    
    func saveVideoContent(
        title: String,
        videoURL: URL,
        duration: Double,
        thumbnail: Data? = nil,
        metadata: [String: String] = [:]
    ) {
        guard let context = modelContext else {
            print("❌ ModelContext not available")
            return
        }
        
        // Get file size
        let fileSize = getFileSize(at: videoURL)
        
        let item = RecentActivityItem(
            title: title,
            contentType: .video,
            filePath: videoURL.path,
            fileSize: fileSize,
            duration: duration,
            thumbnail: thumbnail,
            metadata: metadata
        )
        
        context.insert(item)
        
        do {
            try context.save()
            print("✅ Video saved to Recent Activity: \(title)")
        } catch {
            print("❌ Failed to save video: \(error.localizedDescription)")
        }
    }
    
    func saveScriptContent(
        title: String,
        content: String,
        filePath: String? = nil,
        metadata: [String: String] = [:]
    ) {
        guard let context = modelContext else {
            print("❌ ModelContext not available")
            return
        }
        
        let wordCount = content.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }.count
        
        let fileSize = filePath != nil ? getFileSize(at: URL(fileURLWithPath: filePath!)) : Int64(content.utf8.count)
        
        let item = RecentActivityItem(
            title: title,
            contentType: .script,
            filePath: filePath,
            fileSize: fileSize,
            wordCount: wordCount,
            content: content,
            metadata: metadata
        )
        
        context.insert(item)
        
        do {
            try context.save()
            print("✅ Script saved to Recent Activity: \(title)")
        } catch {
            print("❌ Failed to save script: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Cleanup Operations
    
    func cleanupExpiredContent() {
        guard let context = modelContext else { return }
        
        let fetchDescriptor = FetchDescriptor<RecentActivityItem>()
        
        do {
            let allItems = try context.fetch(fetchDescriptor)
            let expiredItems = allItems.filter { $0.isExpired }
            
            for item in expiredItems {
                // Delete associated file
                item.deleteFile()
                
                // Remove from database
                context.delete(item)
            }
            
            if !expiredItems.isEmpty {
                try context.save()
                print("✅ Cleaned up \(expiredItems.count) expired items")
            }
        } catch {
            print("❌ Failed to cleanup expired content: \(error.localizedDescription)")
        }
    }
    
    func deleteItem(_ item: RecentActivityItem) {
        guard let context = modelContext else { return }
        
        // Delete associated file
        item.deleteFile()
        
        // Remove from database
        context.delete(item)
        
        do {
            try context.save()
            print("✅ Deleted item: \(item.title)")
        } catch {
            print("❌ Failed to delete item: \(error.localizedDescription)")
        }
    }
    
    // MARK: - Helper Methods
    
    private func getFileSize(at url: URL) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}
