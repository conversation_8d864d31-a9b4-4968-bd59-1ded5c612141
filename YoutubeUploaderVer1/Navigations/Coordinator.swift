//
//  Coordinator.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 17/04/25.
//

import Foundation
import SwiftUI

enum CreatorTab: String, CaseIterable, Identifiable {
    case overview = "Overview"
    case videos = "Videos"
    case playlists = "Playlists"
    case recentActivity = "Recent Activity"
    case aiScriptWriter = "AI Script Writer"
//    case uploadQueue = "Upload Videos"
    case uploadVideos = "Upload Video"
//    case aiScriptWriter = "AI Script Writer"

    var id: String { self.rawValue }

    var iconName: String {
        switch self {
        case .overview: return "house.fill"
        case .videos: return "film.fill"
        case .playlists: return "music.note.list"
        case .recentActivity: return "clock.fill"
        case .uploadVideos: return "arrow.up.circle.fill"
        case .aiScriptWriter: return "pencil"
        }
    }
}


final class Coordinator: ObservableObject {
    @Published var selectedTab: CreatorTab = .overview
    
    @ViewBuilder
    func viewForSelectedTab(using navigationCoordinator: NavigationCoordinator) -> some View {
        switch selectedTab {
        case .overview:
            OverviewTab().environmentObject(navigationCoordinator)
        case .videos:
            UserVideosView().environmentObject(navigationCoordinator)
        case .playlists:
            PlayListsView().environmentObject(navigationCoordinator)
        case .recentActivity:
            RecentActivityView().environmentObject(navigationCoordinator)
        case .uploadVideos:
            VideosUploadView().environmentObject(navigationCoordinator)
        case .aiScriptWriter:
            ScriptWriterView().environmentObject(navigationCoordinator)
        }
    }
}
