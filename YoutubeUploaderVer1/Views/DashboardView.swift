

//
//  DashboardView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI
import GoogleSignIn

struct DashboardView: View {
    let signedInUser: GIDGoogleUser
    @StateObject private var coordinator = Coordinator()
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var selectedTab: CreatorTab = .overview
    @State private var isModalPresented = false
    @AppStorage("isSidebarCollapsed") private var isSidebarCollapsed = false
    let localAIService = LocalAIService.shared
    @EnvironmentObject var sharedVideoHandler: SharedVideoHandler

   
    
    
    var body: some View {
        ZStack {
            // Modern gradient background
            LinearGradient(
                colors: [
                    AppColor.backgroundGradientStart.color,
                    AppColor.backgroundGradientEnd.color
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()

            VStack(spacing: 0) {
                // Top navigation with modern styling
                YTCreatorTopNavBar(isModalPresented: $isModalPresented, selectedTab: $coordinator.selectedTab)

                HStack(alignment: .top, spacing: 0) {
                    // Sidebar with modern styling
                    YTCreatorSideNavBar(
                        selectedTab: $coordinator.selectedTab,
                        isCollapsed: $isSidebarCollapsed
                    )

                    // Main content area
                    NavigationStack(path: $navigationCoordinator.path) {
                        ScrollView {
                            VStack(alignment: .leading) {
                                coordinator.viewForSelectedTab(using: navigationCoordinator)
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .navigationDestination(for: NavigationRoute.self) { route in
                            switch route {
                            case .playlistDetailView:
                                PlaylistDetailView()
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .videoAnalyticsView(let video):
                                VideoAnalyticsGraphView(video: video, isVideos: true)
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .playlistVideoAnalyticsView(let video):
                                VideoAnalyticsGraphView(video: video, isVideos: false)
                                    .padding(24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)

                            case .videoUploadView(let videoUrl):
                                VideosUploadView(videoURL: videoUrl)
                                    .padding(.horizontal, 24)
                                    .navigationBarBackButtonHidden(true)
                                    .background(AppColor.surfacePrimary.color)
                            }
                        }
                    }
                    .background(Color.clear)
                }
                .frame(maxHeight: .infinity)
            }

            // Floating Action Button - Upload Video (only show when not on upload tab)
            if coordinator.selectedTab != .uploadVideos {
                VStack {
                    Spacer()
                    HStack {
                        uploadVideoFloatingButton
                        Spacer()
                    }
                    .padding(.leading, isSidebarCollapsed ? 88 : 256) // Account for sidebar width
                    .padding(.bottom, 24)
                }
            }
        }
        .ignoresSafeArea(.container, edges: .top)
        .onChange(of: coordinator.selectedTab) { _ in
            navigationCoordinator.navigateToNewTab()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ToggleSidebar"))) { _ in
            withAnimation(.easeInOut(duration: 0.3)) {
                isSidebarCollapsed.toggle()
            }
        }

//        .onAppear {
//            sharedVideoHandler.checkForSharedVideo()
//        }
    }

    // MARK: - Floating Action Button
    private var uploadVideoFloatingButton: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                coordinator.selectedTab = .uploadVideos
            }
        }) {
            HStack(spacing: 12) {
                Image(systemName: "arrow.up.circle.fill")
                    .font(AppFontStyle.title3.style.weight(.semibold))
                    .foregroundColor(.white)

                Text("Upload Video")
                    .font(AppFontStyle.callout.style.weight(.semibold))
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 14)
            .background(
                LinearGradient(
                    colors: [
                        AppColor.youtubeRed.color,
                        AppColor.youtubeRed.color.opacity(0.8)
                    ],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(25)
            .shadow(
                color: AppColor.youtubeRed.color.opacity(0.4),
                radius: 12,
                x: 0,
                y: 6
            )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(coordinator.selectedTab == .uploadVideos ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: coordinator.selectedTab)
        .help("Upload a new video")
    }

}
