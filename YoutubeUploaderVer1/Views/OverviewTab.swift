//
//  OverviewTab.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI
import GoogleSignIn

struct OverviewTab: View {
    @StateObject private var viewModel: YouTubeAnalyticsViewModel
    @EnvironmentObject var videoAnalyticsViewModel: YouTubeVideoAnalyticsViewModel
    @EnvironmentObject var googleSignInHelper: GoogleSignInHelper
    
    
    @EnvironmentObject var startupManager: StartupManager
    @State private var showModelDownloadModal = false
    
    var viewsPercentageChange: Double?{
        calculatePercentageChange(current: viewModel.thisWeekData?.views, previous: viewModel.previoiusWeekData?.views)
    }
    var minutesWatchedChange: Double? {
        calculatePercentageChange(current: viewModel.thisWeekData?.estimatedMinutesWatched, previous: viewModel.previoiusWeekData?.estimatedMinutesWatched)
    }
    var subscribersChange: Double? {
        guard
            let current = Int(viewModel.thisWeekData?.statistics?.subscriberCount ?? ""),
            let previous = Int(viewModel.previoiusWeekData?.statistics?.subscriberCount ?? "")
        else { return nil }
        
        return calculatePercentageChange(current: current, previous: previous)
    }
    
    var revenueChangePercentage: Double? {
        guard
            let current = viewModel.thisWeekRevenue,
            let previous = viewModel.previousWeekRevenue
        else { return nil }

        return calculatePercentageChange(current: Int(current), previous: Int(previous))
    }

    
    init( ) {
        _viewModel = StateObject(wrappedValue: YouTubeAnalyticsViewModel())
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 32) {
                // Modern Header Section
                VStack(alignment: .leading, spacing: 16) {
                    // Main title with gradient
                    HStack {
                        VStack(alignment: .leading, spacing: 8) {
                            Text(TextConstants.Overview.creatorDashboard)
                                .font(AppFontStyle.largeTitle.style.bold() )
                                .foregroundStyle(
                                    LinearGradient(
                                        colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )

                            Text("\(TextConstants.Overview.welcomeBack), \(googleSignInHelper.user?.profile?.name ?? "Creator")! Here's what's happening with your channel.")
                                .font(AppFontStyle.title2.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                                .lineLimit(2)
                        }

                        Spacer()
                    }

                    // Stats section header
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(TextConstants.Overview.yourChannelStats)
                                .font(AppFontStyle.title1.style.bold())
                                .foregroundColor(AppColor.textPrimary.color)

                            Text("Real-time analytics and insights")
                                .font(AppFontStyle.body.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                        }

                        Spacer()

                        // Refresh indicator
                        if viewModel.isLoadingAnalytics {
                            ProgressView()
                                .scaleEffect(0.8)
                                .tint(AppColor.accentBlue.color)
                        }
                    }
                }

                // Modern Analytics Grid
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 20),
                    GridItem(.flexible(), spacing: 20),
                    GridItem(.flexible(), spacing: 20),
                    GridItem(.flexible(), spacing: 20)
                ], spacing: 20) {
                    if viewModel.isLoadingAnalytics {
                        ForEach(0..<4) { _ in
                            AnalyticsCardSkeleton()
                                .blinking(duration: 0.75)
                                .frame(height: 140)
                        }
                    }
                    else if let analytics = viewModel.analytics {
                        AnalyticsCardView(
                            title: TextConstants.Overview.totalViews,
                            value: formatNumber(analytics.views),
                            label: TextConstants.Overview.views,
                            percentageChange: viewsPercentageChange ?? 0,
                            iconName: "eye.fill"
                        )

                        AnalyticsCardView(
                            title: TextConstants.Overview.watchTime,
                            value: formatNumber(analytics.estimatedMinutesWatched),
                            label: TextConstants.Overview.minutes,
                            percentageChange: minutesWatchedChange ?? 0,
                            iconName: "clock.fill"
                        )

                        AnalyticsCardView(
                            title: TextConstants.Overview.subscribers,
                            value: formatNumber(Int(analytics.statistics?.subscriberCount ?? "0") ?? 0),
                            label: "",
                            percentageChange: subscribersChange ?? 0,
                            iconName: "person.2.fill"
                        )

                        if let lifetimeRevenue = viewModel.lifetimeRevenue {
                            AnalyticsCardView(
                                title: TextConstants.Overview.totalRevenue,
                                value: "$ \(formatNumber(Int(lifetimeRevenue)))",
                                label: TextConstants.Overview.rupees,
                                percentageChange: revenueChangePercentage,
                                iconName: "dollarsign.circle.fill"
                            )
                        } else {
                            AnalyticsCardView(
                                title: TextConstants.Overview.totalRevenue,
                                value: "$ \(0)",
                                label: TextConstants.Overview.rupees,
                                percentageChange: 0,
                                iconName: "dollarsign.circle.fill"
                            )
                        }
                    }
                    else if let error = viewModel.errorMessage {
                        VStack(spacing: 12) {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .font(AppFontStyle.title1.style)
                                .foregroundColor(AppColor.errorRed.color)

                            Text(error)
                                .font(AppFontStyle.title3.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                                .multilineTextAlignment(.center)
                        }
                        .padding(20)
                        .frame(maxWidth: .infinity, minHeight: 140)
                        .background(AppColor.surfaceSecondary.color)
                        .cornerRadius(16)
                        .gridCellColumns(4)
                    }
                }

                // Top Video Section
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(TextConstants.Overview.topViewedVideo)
                                .font(AppFontStyle.title2.style.bold())
                                .foregroundColor(AppColor.textPrimary.color)

                            Text("*\(TextConstants.Overview.sinceWhen)")
                                .font(AppFontStyle.caption1.style.weight(.medium))
                                .foregroundColor(AppColor.textTertiary.color)
                        }

                        Spacer()

                        // Performance badge
                        HStack(spacing: 6) {
                            Image(systemName: "star.fill")
                                .font(AppFontStyle.caption1.style)
                                .foregroundColor(AppColor.accentOrange.color)

                            Text("Top Performer")
                                .font(AppFontStyle.caption1.style.weight(.semibold))
                                .foregroundColor(AppColor.accentOrange.color)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(AppColor.accentOrange.color.opacity(0.1))
                        .cornerRadius(20)
                    }

                    // Video content
                    if videoAnalyticsViewModel.isLoading {
                        VideoSkeletonView()
                            .blinking(duration: 0.75)
                    }
                    else if let topVideo = videoAnalyticsViewModel.topVideo {
                        VideoItemView(video: topVideo, isVideos: true)
                    }
                    else {
                        // Empty state with modern design
                        VStack(spacing: 16) {
                            ZStack {
                                Circle()
                                    .fill(AppColor.accentBlue.color.opacity(0.1))
                                    .frame(width: 80, height: 80)

                                Image(systemName: "chart.bar.xaxis")
                                    .font(AppFontStyle.title1.style.weight(.medium))
                                    .foregroundColor(AppColor.accentBlue.color)
                            }

                            VStack(spacing: 8) {
                                Text(TextConstants.Overview.noTopVideo)
                                    .font(AppFontStyle.title2.style.weight(.semibold))
                                    .foregroundColor(AppColor.textPrimary.color)

                                Text(TextConstants.Overview.noTopVideoDesc)
                                    .font(AppFontStyle.callout.style.weight(.medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(3)
                            }
                        }
                        .padding(40)
                        .frame(maxWidth: .infinity)
                        .background(AppColor.surfaceSecondary.color)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                        )
                    }
                }


                // Analytics Chart Section
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(TextConstants.Overview.channelAnalytics)
                                .font(AppFontStyle.title2.style.bold())
                                .foregroundColor(AppColor.textPrimary.color)

                            Text("Track your channel's performance over time")
                                .font(AppFontStyle.body.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                        }

                        Spacer()

                        // Modern time range selector
                        Menu {
                            ForEach(TimeRangeOption.allCases) { option in
                                Button(option.displayName) {
                                    viewModel.selectedTimeRange = option
                                    Task {
                                        await viewModel.fetchViewsForSelectedRange()
                                    }
                                }
                            }
                        } label: {
                            HStack(spacing: 8) {
                                Text(viewModel.selectedTimeRange.displayName)
                                    .font(AppFontStyle.body.style.weight(.semibold))
                                    .foregroundColor(AppColor.textPrimary.color)

                                Image(systemName: "chevron.down")
                                    .font(AppFontStyle.caption1.style.weight(.semibold))
                                    .foregroundColor(AppColor.textSecondary.color)
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 10)
                            .background(AppColor.surfaceSecondary.color)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    .onAppear {
                        Task {
                            if viewModel.chartData.isEmpty {
                                await viewModel.fetchViewsForSelectedRange()
                            }
                        }
                    }

                    // Chart content
                    if viewModel.isLoadingChartData && viewModel.chartData.isEmpty {
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                                .tint(AppColor.accentBlue.color)

                            Text(TextConstants.Overview.loadingChart)
                                .font(AppFontStyle.title3.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                        }
                        .frame(height: 320)
                        .frame(maxWidth: .infinity)
                        .background(AppColor.surfaceSecondary.color)
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                        )
                    }
                    else {
                        YouTubeAnalyticsChartView(youtubeAnalyticsViewModel: viewModel)
                            .background(AppColor.surfaceSecondary.color)
                            .cornerRadius(16)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                            )
                            .shadow(
                                color: Color.black.opacity(0.05),
                                radius: 8,
                                x: 0,
                                y: 4
                            )
                    }
                }

                Spacer(minLength: 32)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
        }
        .background(
            LinearGradient(
                colors: [
                    AppColor.backgroundGradientStart.color,
                    AppColor.backgroundGradientEnd.color
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .sheet(isPresented: $showModelDownloadModal) {
            OnboardingView {
                showModelDownloadModal = false
            }
        }
        .onAppear {
            let modelExists =  startupManager.checkModelExist(named: "Gemma" )
            
            
            if !startupManager.isModelReady && !startupManager.isDownloading {
                showModelDownloadModal = !modelExists
            }
            
            
            Task {
                if viewModel.analytics==nil{
                    await viewModel.loadAnalytics()
                    await viewModel.loadEstimatedRevenue()
                }
            }
            Task {
                await videoAnalyticsViewModel.getTopTrendingVideo()
            }
        }
    }
}
