//
//  PlaylistDetailView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 14/04/25.
//

import SwiftUI

struct PlaylistDetailView: View {
    @EnvironmentObject var navigationCoordinator:NavigationCoordinator
    @ObservedObject var viewModel = PlaylistViewModel.shared
    @State private var selectedVideo: YouTubeVideo? = nil

    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            
            CustomButton(text: "Back to Playlists") {
                navigationCoordinator.goBackOneStep()
            }
            
            // MARK: - Playlist Info Header
            VStack(alignment: .leading, spacing: 6) {
                Text(viewModel.selectedPlaylist?.snippet.title ?? "Title")
                    .font(AppFontStyle.largeTitle.style)
                    .fontWeight(.bold)
                
                if let playlist = viewModel.selectedPlaylist, !playlist.snippet.description.isEmpty {
                    Text(playlist.snippet.description)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.primary.color.opacity(0.85))
                        .lineLimit(2)
                        .truncationMode(.tail)
                } else {
                    Text(TextConstants.Playlists.NoDesc)
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.primary.color.opacity(0.85))
                        .lineLimit(2)
                        .truncationMode(.tail)
                }

            }
            .padding(.top,8)
            .padding(.bottom, 16)
            
            Divider()
            if viewModel.isLoading {
                ScrollView {
//                    LazyVStack(spacing: 16) {
                        ForEach(0..<5) { _ in
                            VideoSkeletonView()
                                .padding(.horizontal)
                                .blinking(duration: 0.75)
                        }
//                    }
                    .padding(.top, 8)
                }
                
                .frame(minHeight: 700,maxHeight:.infinity)
            } else if let error = viewModel.errorMessage {
                Text(error)
                    .foregroundColor(AppColor.youtubeRed.color)
                    .padding()
            }
            
            
            // Video List
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(viewModel.enrichedPlaylistVideos) { video in
                        VideoItemView(video: video, isVideos:false) 
                    }
                }
            }
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading) // Fill space
//        .ignoresSafeArea()
        .task {
            await viewModel.fetchVideosFromPlaylist()
        }
    }
}



