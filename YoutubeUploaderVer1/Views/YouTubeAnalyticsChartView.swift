import SwiftUI
import Charts

struct YouTubeAnalyticsChartView: View {
    @ObservedObject var youtubeAnalyticsViewModel: YouTubeAnalyticsViewModel
    var dateDomain: ClosedRange<Date> {
        guard let minDate = youtubeAnalyticsViewModel.chartData.min(by: { $0.date < $1.date })?.date,
              let maxDate = youtubeAnalyticsViewModel.chartData.max(by: { $0.date < $1.date })?.date else {
            return Date()...Date()
        }
        let padding = (maxDate.timeIntervalSince1970 - minDate.timeIntervalSince1970) * 0.1
        return minDate.addingTimeInterval(-padding)...maxDate.addingTimeInterval(padding)
    }
    
    var viewCountDomain: ClosedRange<Int> {
        let maxViews = youtubeAnalyticsViewModel.chartData.max(by: { $0.viewCount < $1.viewCount })?.viewCount ?? 0
        let padding = max(2, Int(Double(maxViews) * 0.1))
        return 0...(maxViews + padding)
    }
    
    var estimatedMinutesDomain: ClosedRange<Int> {
        let maxMinutes = youtubeAnalyticsViewModel.chartData.max(by: { $0.estimatedMinutesWatched < $1.estimatedMinutesWatched })?.estimatedMinutesWatched ?? 0
        let padding = max(2, Int(Double(maxMinutes) * 0.1))
        return 0...(maxMinutes + padding)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            if youtubeAnalyticsViewModel.chartData.isEmpty {
                // Modern Empty State
                VStack(spacing: 24) {
                    ZStack {
                        Circle()
                            .fill(AppColor.accentBlue.color.opacity(0.1))
                            .frame(width: 80, height: 80)

                        Image(systemName: "chart.bar.xaxis")
                            .font(AppFontStyle.title1.style.weight(.medium))
                            .foregroundColor(AppColor.accentBlue.color)
                    }

                    VStack(spacing: 12) {
                        Text("No Analytics Yet")
                            .font(AppFontStyle.title2.style.bold())
                            .foregroundColor(AppColor.textPrimary.color)

                        Text("Looks like there's no view data for this time period.\nStart uploading videos and come back soon!")
                            .font(AppFontStyle.body.style.weight(.medium))
                            .foregroundColor(AppColor.textSecondary.color)
                            .multilineTextAlignment(.center)
                            .lineLimit(3)
                    }
                }
                .padding(40)
                .frame(maxWidth: .infinity, minHeight: 300)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(AppColor.surfaceSecondary.color)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                        )
                )
            } else {
                // Charts with proper spacing
                VStack(alignment: .leading, spacing: 32) {
                    // Views Chart Section
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Channel Views")
                                    .font(AppFontStyle.title3.style.bold())
                                    .foregroundColor(AppColor.textPrimary.color)

                                Text("Total views over time")
                                    .font(AppFontStyle.callout.style.weight(.medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                            }

                            Spacer()

                            // Views metric badge
                            HStack(spacing: 6) {
                                Image(systemName: "eye.fill")
                                    .font(AppFontStyle.caption1.style)
                                    .foregroundColor(AppColor.accentBlue.color)

                                Text("Views")
                                    .font(AppFontStyle.caption1.style.weight(.semibold))
                                    .foregroundColor(AppColor.accentBlue.color)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppColor.accentBlue.color.opacity(0.1))
                            .cornerRadius(20)
                        }

                        buildChart(
                            yValueKeyPath: \.viewCount,
                            yDomain: viewCountDomain,
                            accentColor: AppColor.accentBlue.color
                        )
                    }
                    .padding(24)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppColor.surfaceSecondary.color)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                            )
                    )

                    // Watch Time Chart Section
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Watch Time")
                                    .font(AppFontStyle.title3.style.bold())
                                    .foregroundColor(AppColor.textPrimary.color)

                                Text("Minutes watched over time")
                                    .font(AppFontStyle.callout.style.weight(.medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                            }

                            Spacer()

                            // Watch time metric badge
                            HStack(spacing: 6) {
                                Image(systemName: "clock.fill")
                                    .font(AppFontStyle.caption1.style)
                                    .foregroundColor(AppColor.accentOrange.color)

                                Text("Minutes")
                                    .font(AppFontStyle.caption1.style.weight(.semibold))
                                    .foregroundColor(AppColor.accentOrange.color)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppColor.accentOrange.color.opacity(0.1))
                            .cornerRadius(20)
                        }

                        buildChart(
                            yValueKeyPath: \.estimatedMinutesWatched,
                            yDomain: estimatedMinutesDomain,
                            accentColor: AppColor.accentOrange.color
                        )
                    }
                    .padding(24)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(AppColor.surfaceSecondary.color)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(AppColor.borderPrimary.color.opacity(0.5), lineWidth: 1)
                            )
                    )
                }
            }
        }
        .padding(24)
    }
    
    private func buildChart(yValueKeyPath: KeyPath<DailyViewData, Int>, yDomain: ClosedRange<Int>, accentColor: Color) -> some View {
        Chart {
            ForEach(youtubeAnalyticsViewModel.chartData) { dailyData in
                // Area fill with gradient
                AreaMark(
                    x: .value("Date", dailyData.date),
                    y: .value("Value", dailyData[keyPath: yValueKeyPath])
                )
                .interpolationMethod(.catmullRom)
                .foregroundStyle(
                    LinearGradient(
                        colors: [
                            accentColor.opacity(0.3),
                            accentColor.opacity(0.1),
                            accentColor.opacity(0.05)
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )

                // Main line
                LineMark(
                    x: .value("Date", dailyData.date),
                    y: .value("Value", dailyData[keyPath: yValueKeyPath])
                )
                .foregroundStyle(accentColor)
                .lineStyle(StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                .interpolationMethod(.catmullRom)

                // Data points
                PointMark(
                    x: .value("Date", dailyData.date),
                    y: .value("Value", dailyData[keyPath: yValueKeyPath])
                )
                .foregroundStyle(accentColor)
                .symbol(Circle())
                .symbolSize(CGSize(width: 6, height: 6))
            }
        }
        .chartXScale(domain: dateDomain)
        .chartYScale(domain: yDomain)
        .chartXAxis {
            AxisMarks(preset: .automatic, values: .automatic(desiredCount: 6)) { value in
                AxisTick()
                    .foregroundStyle(AppColor.borderPrimary.color.opacity(0.5))
                AxisGridLine()
                    .foregroundStyle(AppColor.borderPrimary.color.opacity(0.2))

                if let date = value.as(Date.self) {
                    if youtubeAnalyticsViewModel.selectedTimeRange == .last7Days || youtubeAnalyticsViewModel.selectedTimeRange == .last30Days {
                        AxisValueLabel(date.formattedAsDateMonth())
                            .foregroundStyle(AppColor.textSecondary.color)
                            .font(AppFontStyle.footnote.style.weight(.medium))
                    }
                    else if youtubeAnalyticsViewModel.selectedTimeRange == .lifeTime {
                        AxisValueLabel(date.formattedAsYear())
                            .foregroundStyle(AppColor.textSecondary.color)
                            .font(AppFontStyle.footnote.style.weight(.medium))
                    }
                    else {
                        AxisValueLabel(date.formattedAsMonthYear())
                            .foregroundStyle(AppColor.textSecondary.color)
                            .font(AppFontStyle.footnote.style.weight(.medium))
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(preset: .aligned, values: .automatic(desiredCount: 5)) { value in
                AxisTick()
                    .foregroundStyle(AppColor.borderPrimary.color.opacity(0.5))
                AxisGridLine()
                    .foregroundStyle(AppColor.borderPrimary.color.opacity(0.2))

                AxisValueLabel()
                    .foregroundStyle(AppColor.textSecondary.color)
                    .font(AppFontStyle.footnote.style.weight(.medium))
            }
        }
        .frame(height: 280)
        .padding(20)
        .background(Color.clear)
    }

}
