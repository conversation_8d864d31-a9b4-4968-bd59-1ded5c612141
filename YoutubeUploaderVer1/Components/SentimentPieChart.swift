//
//  SentimentPieChart.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 08/06/25.
//

import SwiftUI
import Charts

struct SentimentData: Identifiable {
    let id = UUID()
    let label: String
    let count: Int
    let color: Color
}

struct SentimentPieChart: View {
    let sentiments: [SentimentData]

    var total: Int {
        sentiments.map { $0.count }.reduce(0, +)
    }

    var nonZeroSentiments: [SentimentData] {
        sentiments.filter { $0.count > 0 }
    }

    // Helper function to calculate percentage
    private func calculatePercentage(for count: Int) -> Double {
        let safeTotal = max(total, 1)
        return Double(count) / Double(safeTotal) * 100.0
    }

    // Helper function to format percentage string
    private func formatPercentage(for count: Int) -> String {
        let percentage = calculatePercentage(for: count)
        return String(format: "%.1f", percentage)
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(spacing: 24) {
                // Simple SectorMark Pie Chart
                pieChartView

                // Legend
                legendView
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.darkGrayBackground.color.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
                )
        )
    }

    // MARK: - Pie Chart using SectorMark
    private var pieChartView: some View {
        ZStack {
            if total > 0 {
                Chart(nonZeroSentiments, id: \.label) { sentiment in
                    SectorMark(
                        angle: .value("Count", sentiment.count),
                        innerRadius: .ratio(0.5),
                        angularInset: 2.0
                    )
                    .foregroundStyle(sentiment.color.gradient)
                }
                .frame(width: 180, height: 180)
            } else {
                // Empty state
                ZStack {
                    Circle()
                        .stroke(AppColor.grayText.color.opacity(0.3), lineWidth: 2)
                        .frame(width: 180, height: 180)

                    VStack(spacing: 8) {
                        Image(systemName: "bubble.left.and.bubble.right")
                            .font(AppFontStyle.largeTitle.style)
                            .foregroundColor(AppColor.grayText.color.opacity(0.5))

                        Text("No Comments")
                            .font(AppFontStyle.subheadline.style)
                            .foregroundColor(AppColor.grayText.color)
                    }
                }
            }
        }
    }

    // MARK: - Legend
    private var legendView: some View {
        VStack(alignment: .leading, spacing: 12) {
            ForEach(sentiments, id: \.label) { sentiment in
                HStack(spacing: 12) {
                    // Color indicator
                    RoundedRectangle(cornerRadius: 4)
                        .fill(sentiment.color)
                        .frame(width: 16, height: 16)

                    VStack(alignment: .leading, spacing: 2) {
                        Text(sentiment.label)
                            .font(AppFontStyle.subheadline.style.weight(.medium))
                            .foregroundColor(AppColor.primary.color)

                        HStack(spacing: 8) {
                            Text("\(sentiment.count)")
                                .font(AppFontStyle.caption1.style.weight(.semibold))
                                .foregroundColor(sentiment.color)

                            Text("(\(formatPercentage(for: sentiment.count))%)")
                                .font(AppFontStyle.caption1.style)
                                .foregroundColor(AppColor.grayText.color)
                        }
                    }

                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.clear)
                )
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}


