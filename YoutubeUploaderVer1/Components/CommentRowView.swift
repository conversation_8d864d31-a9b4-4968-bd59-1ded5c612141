//
//  CommentRowView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 29/04/25.
//

import SwiftUI
import CoreML

struct CommentRowView: View {
    let author: String
    let content: String
    let profileURL: URL?
    let commentID: String
    let isTopLevelComment:Bool
    let publishedAt: String
    
    @State private var showReplyField: Bool = false
    @State private var replyText: String = ""
    @StateObject private var viewModel = CommentClassifierViewModel()
    @StateObject private var commentsViewModel = CommentViewModel()
    @StateObject var localAIService = LocalAIService.shared
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Main comment content
            HStack(alignment: .top, spacing: 12) {
                // Avatar with better styling
                AsyncImage(url: profileURL) { phase in
                    if let image = phase.image {
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } else {
                        Circle()
                            .fill(AppColor.grayText.color.opacity(0.3))
                            .overlay(
                                Image(systemName: "person.fill")
                                    .foregroundColor(.gray)
                            )
                    }
                }
                .frame(width: 40, height: 40)
                .clipShape(Circle())
                .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
                
                VStack(alignment: .leading, spacing: 6) {
                    HStack {
                        Text(author)
                            .font(AppFontStyle.title3.style)
                            .fontWeight(.bold)
                            .foregroundColor(AppColor.primary.color)
                        
                        Spacer()
                        
                        // Time ago indicator
                        Text(formattedTimeAgo(from: publishedAt))
                            .font(AppFontStyle.body.style)
                            .foregroundColor(AppColor.grayText.color)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(AppColor.darkBackground.color.opacity(0.3))
                            )
                    }
                    
                    Text(content)
                        .font(AppFontStyle.title2.style)
                        .foregroundColor(AppColor.primary.color.opacity(0.9))
                        .fixedSize(horizontal: false, vertical: true)
                        .lineSpacing(2)
                    
                    // Auto reply button
                    if isTopLevelComment{
                        Button {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                showReplyField.toggle()
                                if showReplyField {
                                    Task{
                                      //  await localAIService.initializeModel()
                                        replyText =  await viewModel.autogenerateComment(comment: content, using: localAIService)
                                    }
                                }
                            }
                        } label: {
                            HStack(spacing: 6) {
                                Image(systemName: "arrowshape.turn.up.left.fill")
                                    .font(AppFontStyle.body.style)
                                Text("Auto Reply")
                                    .font(AppFontStyle.body.style)
                                    .fontWeight(.bold)
                            }
                            .foregroundColor(AppColor.youtubeRed.color)
                            .padding(.vertical, 6)
                            .padding(.horizontal, 10)
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(AppColor.youtubeRed.color.opacity(0.1))
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.top, 4)
                    }
                    
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(.vertical, 10)
            
            // Reply field (conditionally shown)
            if showReplyField {
                VStack(spacing: 12) {
                    Divider()
                        .padding(.vertical, 4)
                    
                    HStack(alignment: .top, spacing: 12) {
                        // Indentation and reply indicator
                        HStack(spacing: 0) {
                            Rectangle()
                                .fill(AppColor.grayText.color.opacity(0.3))
                                .frame(width: 2)
                                .padding(.leading, 20)
                        }
                        
                        // Reply input field and post button
                        VStack{
                            HStack(spacing: 8) {
                                if viewModel.isGeneratingReply{
                                    
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .frame(height: 10)
                                        .padding(.vertical, 10)
                                        .padding(.horizontal, 16)
                                    Spacer()
                            
                                }
                                else{
                                    RoundedTextField(placeholder: "Reply", text: $replyText)
                                    
                                }
                                
                                Button {
                                    Task{
                                        await commentsViewModel.postReplyComment(commentId: commentID, replyText: replyText)
                                        if commentsViewModel.replySuccessMessage != nil {
                                            withAnimation {
                                                showReplyField = false
                                                replyText = ""
                                            }
                                        }
                                    }
                                } label: {
                                    if commentsViewModel.isReplying{
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .frame(height: 10)
                                            .padding(.vertical, 10)
                                            .padding(.horizontal, 16)
                                        
                                    }
                                    else if commentsViewModel.replySuccessMessage != nil{
                                        Image(systemName:"checkmark.circle.fill")
                                            .foregroundStyle(.green)
                                            .frame(height: 10)
                                            .padding(.vertical, 10)
                                            .padding(.horizontal, 16)
                                        
                                    }
                                    else{
                                        Text("Post")
                                            .font(AppFontStyle.body.style)
                                            .fontWeight(.bold)
                                            .foregroundColor(.white)
                                            .padding(.vertical, 10)
                                            .padding(.horizontal, 16)
                                            .background(
                                                RoundedRectangle(cornerRadius: 20)
                                                    .fill(AppColor.youtubeRed.color)
                                            )
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                                .disabled(replyText.isEmpty)
                                .opacity(replyText.isEmpty ? 0.6 : 1.0)
                            }
                            if let errorMessage = commentsViewModel.errorMessage{
                                Text(errorMessage)
                                    .font(AppFontStyle.caption1.style)
                                    .foregroundColor(AppColor.youtubeRed.color)
                            }
                        }
                    }
                }
                .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
    
}

