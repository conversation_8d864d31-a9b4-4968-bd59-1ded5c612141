//
//  RoundedTextField.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/04/25.
//

import SwiftUI

struct RoundedTextField: View {
    var placeholder: String
    @Binding var text: String
    var isRequired: Bool = false
    var showValidation: Bool = false
    private var isEmpty: Bool {
           return isRequired && text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
       }
       
       private var isInvalid: Bool {
           return showValidation && isEmpty
       }
    var body: some View {
        HStack(spacing: 6) {
            
            TextField("\(placeholder)", text: $text)
                .tint(.white)
                .font(AppFontStyle.headline.style)
                .textFieldStyle(PlainTextFieldStyle())
                .padding(.leading, 4)
            if isInvalid {
                Image(systemName: "exclamationmark.circle.fill")
                    .foregroundColor(.red)
                    .font(AppFontStyle.title2.style)
                    .padding(.trailing, 8)
            }
            
        }
        .padding(.horizontal, 10)
        .frame(maxWidth: .infinity)
        .frame(height:50)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(AppColor.darkGrayBackground.color)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(isInvalid ? AppColor.youtubeRed.color : Color.clear, lineWidth: 1.5)
                )
        )
        .cornerRadius(20)
        if isInvalid {
            Text("This field is required")
                .font(AppFontStyle.subheadline.style)
                .foregroundColor(.red)
                .padding(.leading, 12)
                .transition(.opacity)
        }
    }
}

struct LargeRoundedTextField: View {
    var placeholder: String
    @Binding var text: String
    var isRequired: Bool = false
    var showValidation: Bool = false

    private var isEmpty: Bool {
        return isRequired && text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    private var isInvalid: Bool {
        return showValidation && isEmpty
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            ZStack(alignment: .topLeading) {
                if text.isEmpty {
                    Text(placeholder)
                        .foregroundColor(.gray)
                        .padding(.horizontal, 14)
                        .padding(.vertical, 12)
                        .font(AppFontStyle.headline.style)
                }

                TextEditor(text: $text)
                    .tint(.white)
                    .font(AppFontStyle.headline.style)
                    .scrollContentBackground(.hidden)
                    .background(AppColor.darkGrayBackground.color)
                    .padding(14)
            }
            .frame(minHeight: 150)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(AppColor.darkGrayBackground.color)
                    .overlay(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(isInvalid ? AppColor.youtubeRed.color : Color.clear, lineWidth: 1.5)
                    )
            )
            .cornerRadius(20)

            if isInvalid {
                HStack(spacing: 6) {
                    Image(systemName: "exclamationmark.circle.fill")
                        .foregroundColor(.red)
                    Text("This field is required")
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(.red)
                }
                .padding(.leading, 12)
                .transition(.opacity)
            }
        }
    }
}

//#Preview {
//    RoundedTextField()
//}
