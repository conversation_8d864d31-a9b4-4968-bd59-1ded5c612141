//
//  TopBarActionButton.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 23/06/25.
//

import SwiftUI

/// A reusable action button component for the top navigation bar
/// Provides consistent styling and improved UX with hover effects and clear labeling
struct TopBarActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    let isActive: Bool
    let helpText: String
    let style: ButtonStyle
    
    enum ButtonStyle {
        case primary
        case secondary
        case accent
    }
    
    init(
        title: String,
        icon: String,
        action: @escaping () -> Void,
        isActive: Bool = false,
        helpText: String,
        style: ButtonStyle = .secondary
    ) {
        self.title = title
        self.icon = icon
        self.action = action
        self.isActive = isActive
        self.helpText = helpText
        self.style = style
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                
                if !title.isEmpty {
                    Text(title)
                        .font(.system(size: 12, weight: .medium))
                }
            }
            .foregroundColor(foregroundColor)
            .padding(.horizontal, title.isEmpty ? 8 : 10)
            .padding(.vertical, 6)
            .background(backgroundColor)
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
        .help(helpText)
        .scaleEffect(isActive ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isActive)
    }
    
    private var foregroundColor: Color {
        switch style {
        case .primary:
            return .white
        case .secondary:
            return isActive ? AppColor.youtubeRed.color : AppColor.textPrimary.color
        case .accent:
            return AppColor.youtubeRed.color
        }
    }

    private var backgroundColor: Color {
        switch style {
        case .primary:
            return AppColor.youtubeRed.color
        case .secondary:
            return isActive ? AppColor.youtubeRed.color.opacity(0.1) : AppColor.surfaceSecondary.color
        case .accent:
            return AppColor.youtubeRed.color.opacity(0.1)
        }
    }
}

/// A specialized badge component for showing counts or status indicators
struct TopBarBadge: View {
    let count: Int
    let color: Color
    
    init(count: Int, color: Color = AppColor.youtubeRed.color) {
        self.count = count
        self.color = color
    }
    
    var body: some View {
        if count > 0 {
            Text("\(count)")
                .font(.system(size: 10, weight: .bold))
                .foregroundColor(.white)
                .frame(minWidth: 16, minHeight: 16)
                .background(color)
                .clipShape(Circle())
                .overlay(
                    Circle()
                        .stroke(Color.white, lineWidth: 1)
                )
        }
    }
}

#Preview {
    HStack(spacing: 12) {
        TopBarActionButton(
            title: "Theme",
            icon: "sun.max.fill",
            action: {},
            helpText: "Change app theme"
        )
        
        TopBarActionButton(
            title: "Upload",
            icon: "plus.circle.fill",
            action: {},
            helpText: "Upload new video",
            style: .primary
        )
        
        TopBarActionButton(
            title: "",
            icon: "person.fill",
            action: {},
            helpText: "User menu"
        )
    }
    .padding()
    .background(AppColor.surfacePrimary.color)
}
