//
//  ShortsClipsCreationView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 02/06/25.
//

import SwiftUI
import AVFoundation
import AVKit
import AppKit
import UniformTypeIdentifiers


struct ShortsClipsCreationView: View {
    @State private var keyword: String = ""
    @State private var clipDuration: Double = 30
    @State private var selectedClipCount: Int = 1
    @State var videoURL: URL?
    @State private var mergedVideoURL: URL?
    @State private var player: AVPlayer? = nil
    @State private var selectedPlatform: Platform = .youtube
    @ObservedObject var viewModel: VideoSummaryViewModel
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    
    @State private var rotationAngle: Double = 0
    @State private var currentIconIndex = 0
    let loadingIcons = ["bolt"]
    
    @State private var isLoadingShortsClips: Bool = false
    @State private var isVideoGenerationFailed: Bool = false
    
    @State var shortsClipLoaderText:String = "Extracting related segments"
    
    //    enum Platform: String, CaseIterable {
    //        case tiktok = "TikTok"
    //        case youtube = "YouTube"
    //        case instagram = "Instagram"
    //    }
    //
    var body: some View {
        VStack(alignment: .leading,spacing:20) {
           // Label("Extract a short video clip using your prompt", systemImage: "scissors.badge.ellipsis")
            HStack {
                Image(systemName: "scissors.badge.ellipsis")
                    .font(AppFontStyle.title1.style)
                    .foregroundColor(AppColor.youtubeRed.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Shorts and Clip Creation")
                        .font(AppFontStyle.headline.style.weight(.bold))
                        .foregroundColor(AppColor.primary.color)
                    
                    Text("Extract a short video clip using your prompt")
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.grayText.color)
                }
                
                Spacer()
            }
            
            RoundedTextField(placeholder: "Enter a topic or concept you'd like a video about... ", text: $keyword, isRequired: true)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppColor.grayText.color.opacity(0.6), lineWidth: 1)
                )
            
            // Clip Duration
            VStack(alignment: .leading,spacing: 8) {
                Text("Choose duration per clip (seconds):")
                    .font(AppFontStyle.headline.style)
                    .foregroundColor(AppColor.primary.color)
                
                // Value shown above thumb
                GeometryReader { geometry in
                    let sliderWidth = geometry.size.width
                    let normalized = (clipDuration - 20) / (60 - 20)
                    let thumbX = normalized * sliderWidth
                    
                    ZStack(alignment: .leading) {
                        Text("\(Int(clipDuration))s")
                            .font(AppFontStyle.caption1.style)
                            .foregroundColor(.blue)
                            .offset(x: max(0, min(thumbX - 5, sliderWidth - 20))) // Center above thumb
                    }
                    .frame(height: 20)
                }
                .frame(height: 20)
                
                Slider(value: $clipDuration, in: 20...60, step: 10)
                    .accentColor(AppColor.grayText.color)
            }
            
            //            HStack(alignment: .top, spacing: 32) {
            
            // MARK: Number of Clips
//            VStack(alignment: .leading, spacing: 16) {
//                Text("Number of Clips to Generate")
//                    .font(AppFontStyle.headline.style)
//                    .foregroundColor(AppColor.primary.color)
//                
//                HStack(spacing: 12) {
//                    ForEach([1, 3, 5, 10], id: \.self) { count in
//                        Button(action: {
//                            selectedClipCount = count
//                        }) {
//                            Text("\(count)")
//                                .font(AppFontStyle.body.style)
//                                .foregroundColor(selectedClipCount == count ? .white : AppColor.primary.color)
//                                .frame(height:44)
//                                .frame(maxWidth: .infinity)
//                                .background(
//                                    selectedClipCount == count
//                                    ? AppColor.youtubeRed.color
//                                    : AppColor.darkGrayBackground.color
//                                )
//                                .cornerRadius(8)
//                                .overlay(
//                                    RoundedRectangle(cornerRadius: 8)
//                                        .stroke(AppColor.grayText.color.opacity(0.6))
//                                )
//                        }
//                    }
//                }
//                .frame(width:300)
//                .buttonStyle(PlainButtonStyle())
//            }
            
            // MARK: Optimization Target with Icons
            //                VStack(alignment: .leading, spacing: 16) {
            //                    Text("Optimization Target")
            //                        .font(AppFontStyle.headline.style)
            //                        .foregroundColor(AppColor.primary.color)
            //
            //                    HStack(spacing: 16) {
            //                        ForEach(Platform.allCases, id: \.self) { platform in
            //                            Button(action: {
            //                                selectedPlatform = platform
            //                            }) {
            //                                Image(platform.iconName)
            //                                    .resizable()
            //                                    .scaledToFit()
            //                                    .frame(width: 32, height: 32)
            //
            //                                    .padding(6)
            //                                    .foregroundColor(selectedPlatform == platform ? .white : AppColor.primary.color)
            //                                    .background(
            //                                        selectedPlatform == platform
            //                                        ? AppColor.youtubeRed.color
            //                                        : AppColor.darkGrayBackground.color
            //                                    )
            //                                    .cornerRadius(8)
            //                                    .overlay(
            //                                        RoundedRectangle(cornerRadius: 8)
            //                                            .stroke(AppColor.grayText.color.opacity(0.6))
            //                                    )
            //                            }
            //                            .padding(.vertical,6)
            //                            .frame(width: 44, height: 44)
            //                        }
            //                    }
            //
            //                    .buttonStyle(PlainButtonStyle())
            //                }
            //            }
            
            if let player = player {
                Text("Generated Clip:")
                    .font(.headline)
                    .padding(.top, 10)

                VideoPlayer(player: player)
                    .aspectRatio(16/9, contentMode: .fit)
                    .cornerRadius(12)
                    .shadow(radius: 5)
                    

            }
            
            if isVideoGenerationFailed {
                Text("Video generation failed. The topic may be too broad, irrelevant, or there was an unexpected issue. Please try again with a more specific or different topic.")
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding()
            }

            
            
            
            
            if viewModel.isExtractingSegments || isLoadingShortsClips {
                HStack(alignment:.center, spacing: 10) {
                    Image(systemName: loadingIcons[currentIconIndex])
                        .rotationEffect(.degrees(rotationAngle))
                        .animation(.easeInOut(duration: 0.6), value: rotationAngle)
                        .onAppear {
                            startIconAnimation()
                        }
                    
                    Text(shortsClipLoaderText)
                        .font(.headline)
                        .foregroundColor(AppColor.grayText.color)
                }
                .frame(maxWidth: .infinity)
            } else {
                Button(action: {
                    Task {
                        await viewModel.getRelatedTranscriptSegments(from: transcriptItems, keyword: keyword,clipDuration: Int(clipDuration))
                        if let relatedSegments = viewModel.relatedSegments {
                        let segments = extractPotentialSegments(from: relatedSegments)
                        saveMergedVideo(transcript: segments)

                        }
                    }
                }){
                    HStack {
                        Image(systemName: "bolt.fill")
                            .foregroundColor(.white)
                        Text("Generate Clip")
                            .fontWeight(.semibold)
                            .font(AppFontStyle.headline.style)
                        
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(AppColor.youtubeRed.color.opacity(0.6))
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .shadow(radius: 5)
                }
                .padding(.horizontal)
                .buttonStyle(PlainButtonStyle())
            }
            
            
            
            
            Spacer()
        }
        .ignoresSafeArea()
        .onChange(of: isLoadingShortsClips) { isLoading in
            if isLoading {
                NotificationCenter.default.post(name: NSNotification.Name("ShortsClipsStarted"), object: nil)
            } else {
                NotificationCenter.default.post(name: NSNotification.Name("ShortsClipsCompleted"), object: nil)
            }
        }
        .onChange(of: viewModel.isExtractingSegments) { isExtracting in
            if isExtracting {
                NotificationCenter.default.post(name: NSNotification.Name("ShortsClipsStarted"), object: nil)
            } else {
                NotificationCenter.default.post(name: NSNotification.Name("ShortsClipsCompleted"), object: nil)
            }
        }
    }
    func startIconAnimation() {
        Timer.scheduledTimer(withTimeInterval: 1.2, repeats: true) { _ in
            rotationAngle += 180
            currentIconIndex = (currentIconIndex + 1) % loadingIcons.count
        }
    }
    
    
    
    //fucntiosn
    
    //called first
    func saveMergedVideo(transcript: [PotentialSegment]) {
        
        self.isLoadingShortsClips = true
        
        extractAndMergeVideoClips(from: videoURL!, transcripts: transcript) { mergedURL in
            if let mergedURL = mergedURL {
                
                
                print("✅ Merged video saved at: \(mergedURL.path)")
                self.mergedVideoURL = mergedURL
                self.player = AVPlayer(url: mergedURL)
                self.isLoadingShortsClips = false
                self.shortsClipLoaderText = "Extracting related segments"
                self.isVideoGenerationFailed = false


            } else {
                print("❌ Failed to merge clips.")
                self.isLoadingShortsClips = false
                self.shortsClipLoaderText = "Extracting related segments"
                self.isVideoGenerationFailed = true
                self.player = nil
            }
        }
    }
    
    //called second
    func extractAndMergeVideoClips(from videoURL: URL, transcripts: [PotentialSegment], completion: @escaping (URL?) -> Void) {
        let selectedClips = transcripts.map { ($0.startTime, $0.endTime, $0.text) }
        var extractedURLs: [URL?] = Array(repeating: nil, count: selectedClips.count)  // Ordered array
        let group = DispatchGroup()
        
        for (index, (start, end, _)) in selectedClips.enumerated() {
            group.enter()
            let startTime = CMTime(seconds: start, preferredTimescale: 600)
            let endTime = CMTime(seconds: end, preferredTimescale: 600)

            let transcriptText = selectedClips[index].2
            extractClip(from: videoURL, startTime: startTime, endTime: endTime) { clipURL in
                if let clipURL = clipURL {
                    extractedURLs[index] = clipURL  // Store in the correct index
                }
                group.leave()
            }
        }

        group.notify(queue: .main) {
            let orderedExtractedURLs = extractedURLs.compactMap { $0 }  // Remove nil values

            if orderedExtractedURLs.isEmpty {
                completion(nil)
            } else {
                mergeVideos(videoURLs: orderedExtractedURLs, completion: completion)
            }
        }
    }

    
    func extractClip(from videoURL: URL, startTime: CMTime, endTime: CMTime, completion: @escaping (URL?) -> Void) {
        self.shortsClipLoaderText = "Identifying and extracting relevant video segments..."
        let asset = AVAsset(url: videoURL)
        let exporter = AVAssetExportSession(asset: asset, presetName: AVAssetExportPresetHighestQuality)!
        
        
        
        // Define output file URL
        let outputURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString).appendingPathExtension("mp4")
        
        // Define the time range
        let timeRange = CMTimeRange(start: startTime, end: endTime)
        exporter.timeRange = timeRange
        exporter.outputFileType = .mp4
        exporter.outputURL = outputURL
        
        // Export the clip
        exporter.exportAsynchronously {
            DispatchQueue.main.async {
                completion(exporter.status == .completed ? outputURL : nil)
            }
        }
    }
    
    
    func mergeVideos(videoURLs: [URL], completion: @escaping (URL?) -> Void) {
        self.shortsClipLoaderText = "Combining extracted segments into a single video..."
        let composition = AVMutableComposition()
        
        // Track for video & audio
        let videoTrack = composition.addMutableTrack(withMediaType: .video, preferredTrackID: kCMPersistentTrackID_Invalid)
        let audioTrack = composition.addMutableTrack(withMediaType: .audio, preferredTrackID: kCMPersistentTrackID_Invalid)
        
        Task {
            var insertTime = CMTime.zero  // Move this inside Task to avoid concurrency issues
            
            for videoURL in videoURLs {
                let asset = AVAsset(url: videoURL)
                
                do {
                    // Load tracks asynchronously
                    let videoTracks = try await asset.loadTracks(withMediaType: .video)
                    let audioTracks = try await asset.loadTracks(withMediaType: .audio)
                    
                    guard let videoAssetTrack = videoTracks.first else {
                        print("⚠️ No video track found in \(videoURL.lastPathComponent)")
                        continue
                    }
                    
                    let audioAssetTrack = audioTracks.first  // Some videos may not have audio
                    
                    // Load duration asynchronously
                    let assetDuration = try await asset.load(.duration)
                    
                    // Insert video & audio tracks sequentially
                    try videoTrack?.insertTimeRange(CMTimeRange(start: .zero, duration: assetDuration), of: videoAssetTrack, at: insertTime)
                    
                    if let audioAssetTrack = audioAssetTrack {
                        try audioTrack?.insertTimeRange(CMTimeRange(start: .zero, duration: assetDuration), of: audioAssetTrack, at: insertTime)
                    }
                    
                    insertTime = CMTimeAdd(insertTime, assetDuration) // Move to next clip position
                    
                } catch {
                    print("❌ Error processing \(videoURL.lastPathComponent): \(error.localizedDescription)")
                }
            }
            
            // Export merged video
            let outputURL = FileManager.default.temporaryDirectory.appendingPathComponent("merged_video.mp4")
            
            if FileManager.default.fileExists(atPath: outputURL.path) {
                do {
                    try FileManager.default.removeItem(at: outputURL)
                    print("✅ Existing merged video deleted.")
                } catch {
                    print("❌ Error deleting existing merged video: \(error.localizedDescription)")
                    DispatchQueue.main.async { completion(nil) }
                    return
                }
            }
            
            guard let exporter = AVAssetExportSession(asset: composition, presetName: AVAssetExportPresetHighestQuality) else {
                print("❌ Failed to create exporter")
                DispatchQueue.main.async { completion(nil) }
                return
            }
            
            exporter.outputURL = outputURL
            exporter.outputFileType = .mp4
            
            do {
                self.shortsClipLoaderText = "Saving the final video to your library..."
                await exporter.export()
                DispatchQueue.main.async {
                    completion(exporter.status == .completed ? outputURL : nil)
                }
            }
        }
    }

    
}






