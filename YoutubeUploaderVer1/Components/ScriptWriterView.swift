//
//  ScriptWriterView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI
import MarkdownUI

enum ToolOption: String, CaseIterable {
    case createImage = "Create an image"
    case searchWeb = "Search the web"
    case writeCode = "Write or code"
    case thinkLonger = "Think for longer"
    
    var icon: String {
        switch self {
        case .createImage: return "photo"
        case .searchWeb: return "globe"
        case .writeCode: return "pencil"
        case .thinkLonger: return "lightbulb"
        }
    }
    
    var shortTitle: String {
        switch self {
        case .createImage: return "Image"
        case .searchWeb: return "Search"
        case .writeCode: return "Code"
        case .thinkLonger: return "Think"
        }
    }
}

struct ScriptWriterView: View {
    @StateObject private var scriptManager = ScriptWriterManager()
    @State private var showToolsDropdown = false
    @State private var selectedTool: ToolOption? = nil
    
    let localAIService = LocalAIService.shared
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Modern Header
                modernHeaderSection
                
                // Script Parameters
//                if !scriptManager.isGenerating {
                    modernScriptParametersSection
//                }
                
                
                // Generated Script Display
                if !scriptManager.generatedScript.isEmpty && !scriptManager.isGenerating {
                    modernGeneratedScriptSection
                }
                
                // Generate Button
                modernGenerateButtonSection
            }
            .padding(20)
        }
        .background(AppColor.darkBackground.color)
        .alert("Script Generation Error", isPresented: $scriptManager.showErrorAlert) {
            Button("OK") {
                scriptManager.showErrorAlert = false
            }
        } message: {
            Text(scriptManager.errorMessage ?? "An unknown error occurred")
        }
        .onAppear{
            Task{
                if localAIService.model == nil{
                    await localAIService.initializeModel()
                }
            }
        }
    }
    
    
    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(alignment: .top) {
                // Title and Subtitle with Gradient
                VStack(alignment: .leading, spacing: 8) {
                    Text("AI Script Writer")
                        .font(AppFontStyle.largeTitle.style)
                        .fontWeight(.bold)
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                    
                    Text("Generate professional video scripts with AI-powered intelligence")
                        .font(AppFontStyle.title2.style)
                        .fontWeight(.medium)
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                
            }
            
        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
        .padding(.bottom, 24)
    }
    
    
    
    
    
    private func featureHighlightCard(icon: String, title: String, subtitle: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(AppFontStyle.title2.style)
                .fontWeight(.semibold)
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )
            
            VStack(spacing: 2) {
                Text(title)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                    .foregroundColor(AppColor.textPrimary.color)
                
                Text(subtitle)
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textSecondary.color)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Modern Script Parameters Section
    private var modernScriptParametersSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Script Configuration")
                        .font(AppFontStyle.title1.style)
                        .fontWeight(.bold)
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("Customize your script parameters for optimal results")
                        .font(AppFontStyle.body.style)
                        .fontWeight(.medium)
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                // Configuration Status Badge
//                configurationStatusBadge
            }
            
            VStack(alignment: .leading, spacing: 20) {
                // Topic Input Section
                modernTopicInputSection
                
                // Script Configuration Grid
                modernConfigurationGrid
                
                // Advanced Options
                // modernAdvancedOptionsSection
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }
    
    // MARK: - Configuration Status Badge
    private var configurationStatusBadge: some View {
        HStack(spacing: 6) {
            Image(systemName: isConfigurationComplete ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                .font(AppFontStyle.body.style)
                .fontWeight(.semibold)
                .foregroundColor(isConfigurationComplete ? Color.green : Color.orange)
            
            Text(isConfigurationComplete ? "Ready to Generate" : "Configuration Needed")
                .font(AppFontStyle.caption2.style)
                .fontWeight(.semibold)
                .foregroundColor(isConfigurationComplete ? Color.green : Color.orange)
            
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill((isConfigurationComplete ? Color.green : Color.orange).opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke((isConfigurationComplete ? Color.green : Color.orange).opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    private var isConfigurationComplete: Bool {
        !scriptManager.topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // MARK: - Modern Topic Input Section
    private var modernTopicInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Video Topic")
                    .font(AppFontStyle.title3.style)
                    .fontWeight(.bold)
                
                Text("*")
                    .font(AppFontStyle.title3.style)
                    .fontWeight(.bold)
                
                Spacer()
                
                // Tools section - show selected tool or tools button
                HStack(spacing: 8) {
                    // Show selected tool badge if any tool is selected
                    if let selectedTool = selectedTool {
                        selectedToolBadge(tool: selectedTool)
                    }
                    
                    // Tools dropdown button
                    Button(action: {
                        showToolsDropdown.toggle()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "wrench.and.screwdriver")
                                .font(AppFontStyle.body.style)
                                .fontWeight(.semibold)
                            Text("Tools")
                                .font(AppFontStyle.body.style)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(AppColor.textSecondary.color)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(.plain)
                    .popover(isPresented: $showToolsDropdown, arrowEdge: .bottom) {
                        toolsPopoverContent
                    }
                }
                
                // Character count
                Text("\(scriptManager.topic.count)/500")
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textTertiary.color)
            }
            
            TextField("Describe your video topic or idea in detail...", text: $scriptManager.topic, axis: .vertical)
                .textFieldStyle(.plain)
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(AppColor.surfaceSecondary.color)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    scriptManager.topic.isEmpty ? AppColor.borderPrimary.color.opacity(0.3) : AppColor.accentBlue.color.opacity(0.5),
                                    lineWidth: scriptManager.topic.isEmpty ? 1 : 2
                                )
                        )
                )
                .lineLimit(3...8)
                .font(AppFontStyle.body.style)
                .fontWeight(.medium)
                .foregroundColor(AppColor.textPrimary.color)
            
            Text("\u{1F4A1} Be specific about your topic, target audience, and key points you want to cover")
                .font(AppFontStyle.caption2.style)
                .fontWeight(.medium)
                .foregroundColor(AppColor.textTertiary.color)
                .padding(.horizontal, 4)
        }
    }
    
    // MARK: - Selected Tool Badge
    private func selectedToolBadge(tool: ToolOption) -> some View {
        Button(action: {
            // Clicking the badge deselects the tool
            selectedTool = nil
        }) {
            HStack(spacing: 6) {
                Image(systemName: tool.icon)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(tool.shortTitle)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Image(systemName: "xmark")
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(AppColor.accentBlue.color)
            )
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Tools Popover Content
    private var toolsPopoverContent: some View {
        VStack(alignment: .leading, spacing: 0) {
            ForEach(ToolOption.allCases, id: \.self) { tool in
                
                toolsPopoverItem(
                    tool: tool,
                    action: {
                        selectedTool = tool
                        showToolsDropdown = false
                    }
                )
            }
            
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 4)
        .frame(width: 200)
    }
    
    private func toolsPopoverItem(tool: ToolOption, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 10) {
                Image(systemName: tool.icon)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textSecondary.color)
                    .frame(width: 16, height: 16)
                
                Text(tool.rawValue)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textPrimary.color)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.clear)
            )
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
    
    // MARK: - Modern Configuration Grid
    private var modernConfigurationGrid: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Text("Script Settings")
                    .font(AppFontStyle.title3.style)
                    .fontWeight(.bold)
                    .foregroundColor(AppColor.textPrimary.color)
                
                Spacer()
                
                // Quick Settings Badge
                HStack(spacing: 4) {
                    Image(systemName: "slider.horizontal.3")
                        .font(AppFontStyle.caption2.style)
                        .fontWeight(.medium)
                    Text("Customizable")
                        .font(AppFontStyle.caption2.style)
                        .fontWeight(.semibold)
                }
                .foregroundColor(AppColor.accentBlue.color)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(AppColor.accentBlue.color.opacity(0.1))
                )
            }
            
            // Detailed Selectors
            VStack(spacing: 16) {
                modernScriptTypeSelector
                modernScriptLengthSelector
                modernScriptToneSelector
            }
        }
    }
    
    private func modernConfigOverviewCard(title: String, value: String, icon: String, color: Color, description: String) -> some View {
        VStack(spacing: 12) {
            // Icon with background
            Image(systemName: icon)
                .font(AppFontStyle.title2.style)
                .fontWeight(.semibold)
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(color.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(color.opacity(0.2), lineWidth: 1)
                        )
                )
            
            VStack(spacing: 4) {
                Text(title)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                    .foregroundColor(AppColor.textPrimary.color)
                    .textCase(.uppercase)
                    .tracking(0.5)
                
                Text(value)
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                    .foregroundColor(AppColor.textPrimary.color)
                    .lineLimit(1)
                
                Text(description)
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textTertiary.color)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }
    
    // MARK: - Modern Selectors
    private var modernScriptTypeSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Select Video Type")
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                
                Spacer()
                
                Text("\(ScriptType.allCases.count) Options")
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textTertiary.color)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            scriptManager.selectedType = type
                        }
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(AppFontStyle.body.style)
                                .fontWeight(.semibold)
                                .foregroundColor(scriptManager.selectedType == type ? .white : AppColor.accentBlue.color)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(scriptManager.selectedType == type ? AppColor.accentBlue.color : AppColor.accentBlue.color.opacity(0.1))
                                )
                            
                            Text(type.rawValue)
                                .font(AppFontStyle.caption2.style)
                                .fontWeight(.bold)
                                .foregroundColor(scriptManager.selectedType == type ? AppColor.textPrimary.color : AppColor.textSecondary.color)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 8)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(scriptManager.selectedType == type ? AppColor.surfacePrimary.color : AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            scriptManager.selectedType == type ? AppColor.accentBlue.color : AppColor.borderPrimary.color.opacity(0.3),
                                            lineWidth: scriptManager.selectedType == type ? 2 : 1
                                        )
                                )
                        )
                        .shadow(color: scriptManager.selectedType == type ? AppColor.accentBlue.color.opacity(0.2) : Color.clear, radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                }
            }
            
            if scriptManager.selectedType != .educational {
                Text(scriptManager.selectedType.description)
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.textTertiary.color)
                    .padding(.horizontal, 4)
                    .padding(.top, 4)
            }
        }
    }
    
    private var modernScriptLengthSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Choose Duration")
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                
                Spacer()
                
                HStack(spacing: 4) {
                    Image(systemName: "clock.fill")
                        .font(AppFontStyle.caption2.style)
                        .fontWeight(.medium)
                    Text("Estimated Words")
                        .font(AppFontStyle.caption2.style)
                        .fontWeight(.medium)
                }
                .foregroundColor(AppColor.textTertiary.color)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ScriptLength.allCases, id: \.self) { length in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            scriptManager.selectedLength = length
                        }
                    }) {
                        VStack(spacing: 8) {
                            HStack(spacing: 8) {
                                Image(systemName: "clock.fill")
                                    .font(AppFontStyle.body.style)
                                    .fontWeight(.semibold)
                                
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(length.rawValue)
                                        .font(AppFontStyle.body.style)
                                        .fontWeight(.bold)
                                        .foregroundColor(scriptManager.selectedLength == length ? .white : AppColor.textPrimary.color)
                                    
                                    Text(length.estimatedWords)
                                        .font(AppFontStyle.caption2.style)
                                        .fontWeight(.medium)
                                        .foregroundColor(scriptManager.selectedLength == length ? .white.opacity(0.8) : AppColor.textSecondary.color)
                                }
                                
                                Spacer()
                            }
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(scriptManager.selectedLength == length ? Color.green : AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            scriptManager.selectedLength == length ? Color.green : AppColor.borderPrimary.color.opacity(0.3),
                                            lineWidth: scriptManager.selectedLength == length ? 2 : 1
                                        )
                                )
                        )
                        .shadow(color: scriptManager.selectedLength == length ? Color.green.opacity(0.2) : Color.clear, radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    private var modernScriptToneSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Select Tone & Style")
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                
                Spacer()
                
                HStack(spacing: 4) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(AppFontStyle.caption2.style)
                        .fontWeight(.medium)
                    Text("Voice & Personality")
                        .font(AppFontStyle.caption2.style)
                        .fontWeight(.medium)
                }
                .foregroundColor(AppColor.textTertiary.color)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ScriptTone.allCases, id: \.self) { tone in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            scriptManager.selectedTone = tone
                        }
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: "speaker.wave.2.fill")
                                .font(AppFontStyle.body.style)
                                .fontWeight(.semibold)
                                .foregroundColor(scriptManager.selectedTone == tone ? .white : Color.purple)
                                .frame(width: 28, height: 28)
                                .background(
                                    Circle()
                                        .fill(scriptManager.selectedTone == tone ? Color.purple : Color.purple.opacity(0.1))
                                )
                            
                            VStack(spacing: 4) {
                                Text(tone.rawValue)
                                    .font(AppFontStyle.body.style)
                                    .fontWeight(.bold)
                                    .foregroundColor(scriptManager.selectedTone == tone ? AppColor.textPrimary.color : AppColor.textSecondary.color)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(1)
                                
                                Text(tone.description)
                                    .font(AppFontStyle.caption2.style)
                                    .fontWeight(.medium)
                                    .foregroundColor(scriptManager.selectedTone == tone ? AppColor.textSecondary.color : AppColor.textTertiary.color)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(2)
                            }
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 8)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(scriptManager.selectedTone == tone ? AppColor.surfacePrimary.color : AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            scriptManager.selectedTone == tone ? Color.purple : AppColor.borderPrimary.color.opacity(0.3),
                                            lineWidth: scriptManager.selectedTone == tone ? 2 : 1
                                        )
                                )
                        )
                        .shadow(color: scriptManager.selectedTone == tone ? Color.purple.opacity(0.2) : Color.clear, radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }
    
    
    // MARK: - Modern Generated Script Section
    private var modernGeneratedScriptSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Generated Script")
                    .font(AppFontStyle.title3.style)
                    .fontWeight(.bold)
                    .foregroundColor(AppColor.primary.color)
                
                Spacer()
                
                HStack(spacing: 6) {
                    if !scriptManager.generatedScript.isEmpty {
                        Button(action: {
                            scriptManager.copyScriptToClipboard()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "doc.on.clipboard")
                                    .font(AppFontStyle.body.style)
                                Text(scriptManager.isCopied ? "Copied!" : "Copy")
                                    .font(AppFontStyle.caption2.style)
                                    .fontWeight(.semibold)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppColor.grayText.color.opacity(0.2))
                            .foregroundColor(AppColor.primary.color)
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)
                        
                        Button(action: {
                            scriptManager.exportScript()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(AppFontStyle.body.style)
                                Text("Export")
                                    .font(AppFontStyle.caption2.style)
                                    .fontWeight(.semibold)
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppColor.youtubeRed.color)
                            .foregroundColor(.white)
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)
                    }
                    
                    Button(action: {
                        scriptManager.reset()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.clockwise")
                                .font(AppFontStyle.body.style)
                            Text("New")
                                .font(AppFontStyle.caption2.style)
                                .fontWeight(.semibold)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppColor.grayText.color.opacity(0.2))
                        .foregroundColor(AppColor.primary.color)
                        .cornerRadius(6)
                    }
                    .buttonStyle(.plain)
                }
            }
            
            ScrollView {
                Markdown(scriptManager.generatedScript)
                    .font(AppFontStyle.body.style)
                    .foregroundColor(AppColor.primary.color)
                    .textSelection(.enabled)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(12)
            }
            .frame(minHeight: 300, maxHeight: 500)
            .background(AppColor.darkGrayBackground.color)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
            )
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
    }
    
    // MARK: - Modern Generate Button Section
    private var modernGenerateButtonSection: some View {
        VStack(spacing: 16) {
            // Pre-generation Summary
//            if !scriptManager.topic.isEmpty && !scriptManager.isGenerating {
//                generationSummaryCard
//            }
            
            // Generate Button
            Button(action: {
                scriptManager.generateScript(selectedTool: selectedTool)
            }) {
                HStack(spacing: 12) {
                    if scriptManager.isGenerating {
                        ProgressView()
                            .scaleEffect(0.9)
                            .frame(width: 20, height: 20)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "wand.and.stars")
                            .font(AppFontStyle.title2.style)
                            .fontWeight(.bold)
                    }
                    
                    VStack(spacing: 2) {
                        Text(scriptManager.isGenerating ? "Generating Script..." : "Generate Script")
                            .font(AppFontStyle.title3.style)
                            .fontWeight(.bold)
                    }
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 18)
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [AppColor.youtubeRed.color, AppColor.youtubeRed.color.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .foregroundColor(.white)
                .cornerRadius(12)
                .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .buttonStyle(.plain)
            .disabled(scriptManager.isGenerating || !isConfigurationComplete || !localAIService.isModelInitalized)
            .opacity(scriptManager.isGenerating || !isConfigurationComplete || !localAIService.isModelInitalized ? 0.6 : 1.0)
            
            // Generation Tips
            if !scriptManager.isGenerating && !isConfigurationComplete {
                HStack(spacing: 8) {
                    Image(systemName: "info.circle.fill")
                        .font(AppFontStyle.body.style)
                        .fontWeight(.medium)
                        .foregroundColor(Color.orange)
                    
                    Text("Please enter a video topic to generate your script")
                        .font(AppFontStyle.body.style)
                        .fontWeight(.medium)
                        .foregroundColor(AppColor.textSecondary.color)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.orange.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                )
            }
        }
    }
    
    // MARK: - Generation Summary Card
    private var generationSummaryCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Ready to Generate")
                    .font(AppFontStyle.body.style)
                    .fontWeight(.bold)
                    .foregroundColor(AppColor.textPrimary.color)
                
                Spacer()
                
                Image(systemName: "checkmark.circle.fill")
                    .font(AppFontStyle.body.style)
                    .fontWeight(.semibold)
                    .foregroundColor(Color.green)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                summaryRow(title: "Type", value: scriptManager.selectedType.rawValue)
                summaryRow(title: "Length", value: scriptManager.selectedLength.rawValue)
                summaryRow(title: "Tone", value: scriptManager.selectedTone.rawValue)
                
                if !scriptManager.targetAudience.isEmpty {
                    summaryRow(title: "Audience", value: scriptManager.targetAudience)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.green.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.green.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    private func summaryRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(AppFontStyle.body.style)
                .fontWeight(.medium)
                .foregroundColor(AppColor.textSecondary.color)
            
            Spacer()
            
            Text(value)
                .font(AppFontStyle.body.style)
                .fontWeight(.semibold)
                .foregroundColor(AppColor.textPrimary.color)
        }
    }
}

struct ScriptTypeSelector: View {
    @Binding var selectedType: ScriptType
    
    let columns = [
        GridItem(.adaptive(minimum: 140), spacing: 12)
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Video Type")
                .font(AppFontStyle.title1.style)
                .fontWeight(.bold)
                .foregroundColor(AppColor.primary.color)
            
            LazyVGrid(columns: columns, spacing: 12) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        selectedType = type
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(AppFontStyle.body.style)
                                .fontWeight(.semibold)
                                .foregroundColor(selectedType == type ? .white : AppColor.youtubeRed.color)
                            
                            Text(type.rawValue)
                                .font(AppFontStyle.caption2.style)
                                .fontWeight(.bold)
                                .foregroundColor(selectedType == type ? .white : AppColor.primary.color)
                                .multilineTextAlignment(.center)
                        }
                        .padding(12)
                        .frame(minHeight: 80)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedType == type ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    selectedType == type ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: selectedType == type ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }
            
            if selectedType != .educational {
                Text(selectedType.description)
                    .font(AppFontStyle.caption2.style)
                    .fontWeight(.medium)
                    .foregroundColor(AppColor.grayText.color)
                    .padding(.top, 4)
            }
        }
    }
}

#Preview {
    ScriptWriterView()
}
