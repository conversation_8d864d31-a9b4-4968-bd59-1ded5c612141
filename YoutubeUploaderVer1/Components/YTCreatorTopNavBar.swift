//
//  YTCreatorTopNavBar.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 06/04/25.
//

import SwiftUI
import AVKit

struct YTCreatorTopNavBar: View {
    @State private var searchText: String = ""
    @State private var showLogoutConfirmation = false
    @State private var showThemeMenu = false
    @State private var showUserMenu = false
    @Binding var isModalPresented: Bool
    @EnvironmentObject var signInHelper: GoogleSignInHelper
    @AppStorage("themePreference") var themePreference: ThemePreference = .system
    @Environment(\.colorScheme) var currentEnvironmentScheme
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @Binding var selectedTab: CreatorTab
    @State private var sharedVideoCount: Int = 0
    @State private var showVideoPreview = false
    @State private var sharedVideoURL: URL?
    
    
    var body: some View {
        HStack(spacing: 0) {
            // Enhanced Logo Section with Context
            HStack(spacing: 12) {
                // Logo with subtle animation
                ZStack {
                    RoundedRectangle(cornerRadius: 10)
                        .fill(AppColor.youtubeRed.color)
                        .frame(width: 36, height: 36)

                    Image(systemName: "film.fill")
                        .font(AppFontStyle.callout.style.weight(.semibold))
                        .foregroundColor(.white)
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text("Youtube Uploader")
                        .font(AppFontStyle.headline.style.weight(.bold))
                        .foregroundColor(AppColor.textPrimary.color)

                    // Current section indicator
                    Text(selectedTab.rawValue)
                        .font(AppFontStyle.caption2.style.weight(.medium))
                        .foregroundColor(AppColor.textSecondary.color)
                        .opacity(0.8)
                }
            }

            Spacer()

            // Enhanced Action Items Section with better UX
            HStack(spacing: 12) {
                // Modern Theme Toggle Button
                Menu {
                    ForEach(ThemePreference.allCases) { theme in
                        Button(action: {
                            themePreference = theme
                        }) {
                            HStack {
                                Text(theme.description)
                                Spacer()
                                if themePreference == theme {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppColor.youtubeRed.color)
                                }
                            }
                        }
                    }
                } label: {
                    HStack(spacing: 8) {
                        Image(systemName: {
                            switch currentEnvironmentScheme {
                            case .light: return "sun.max.fill"
                            case .dark: return "moon.fill"
                            @unknown default: return "circle.lefthalf.filled"
                            }
                        }())
                        .font(AppFontStyle.callout.style.weight(.medium))

                        Text("Theme")
                            .font(AppFontStyle.subheadline.style.weight(.semibold))
                    }
                    .foregroundColor(AppColor.textPrimary.color)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 10)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(AppColor.surfaceSecondary.color)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .help("Change app theme")
                .scaleEffect(1.0)
                .animation(.easeInOut(duration: 0.1), value: themePreference)

                // Modern Shared Video Button
                ZStack(alignment: .topTrailing) {
                    Button(action: updateSharedVideoCount) {
                        HStack(spacing: 8) {
                            Image(systemName: "tray.and.arrow.down.fill")
                                .font(AppFontStyle.subheadline.style.weight(.medium))

                            Text("Shared")
                                .font(AppFontStyle.subheadline.style.weight(.semibold))
                        }
                        .foregroundColor(AppColor.textPrimary.color)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 10)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(AppColor.surfaceSecondary.color)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .help("Check for shared videos from other apps")

                    if sharedVideoCount > 0 {
                        Text("\(sharedVideoCount)")
                            .font(AppFontStyle.caption2.style.weight(.bold))
                            .foregroundColor(.white)
                            .frame(width: 18, height: 18)
                            .background(AppColor.youtubeRed.color)
                            .clipShape(Circle())
                            .offset(x: 8, y: -8)
                    }
                }

              

                // Enhanced User Menu with Profile Info
                Menu {
                    // User info section
                    VStack(alignment: .leading, spacing: 4) {
                        if let user = signInHelper.user {
                            Text(user.profile?.name ?? "User")
                                .font(AppFontStyle.subheadline.style.weight(.semibold))
                            Text(user.profile?.email ?? "")
                                .font(AppFontStyle.caption1.style)
                                .foregroundColor(AppColor.textSecondary.color)
                        }
                    }
                    .padding(.vertical, 4)

                    Divider()

                    Button(action: {
                        showLogoutConfirmation = true
                    }) {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                            Text("Sign Out")
                        }
                    }
                } label: {
                    HStack(spacing: 8) {
                        // Modern user avatar
                        ZStack {
                            Circle()
                                .fill(AppColor.surfaceSecondary.color)
                                .frame(width: 32, height: 32)

                            Image(systemName: "person.fill")
                                .font(AppFontStyle.subheadline.style.weight(.medium))
                                .foregroundColor(AppColor.textPrimary.color)
                                .background(AppColor.surfaceSecondary.color)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 50)
                            .fill(AppColor.surfaceSecondary.color)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .help("User menu and settings")
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 12)
        .frame(height: 68) // Slightly taller for better proportions
        .background(
            ZStack {
                // Enhanced background with subtle gradient
                Rectangle()
                    .fill(
                        LinearGradient(
                            colors: [
                                AppColor.surfacePrimary.color,
                                AppColor.surfacePrimary.color.opacity(0.95)
                            ],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .background(.regularMaterial)

                // Enhanced bottom border with gradient
                VStack {
                    Spacer()
                    Rectangle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    AppColor.borderPrimary.color.opacity(0.3),
                                    AppColor.borderPrimary.color.opacity(0.1)
                                ],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .frame(height: 1)
                }
            }
        )
        .alert("Are you sure want to logout ?", isPresented: $showLogoutConfirmation){
            Button("Cancel", role: .cancel) {}
            Button("Logout", role: .destructive) {
                signInHelper.signOut()
            }
        }
        .sheet(isPresented: $showVideoPreview) {
            if let videoURL = sharedVideoURL {
                VideoPreviewSheet(videoURL: videoURL, onUploadTapped: {
//                    selectedTab = .uploadVideos
                    navigationCoordinator.navigateToVideoUploadPage(with: videoURL)
                    showVideoPreview = false
                }, isPresented: $showVideoPreview)
            }
        }
        
        .onAppear {
            updateSharedVideoCount()
        }
    }
    func deleteGGUFFilesInDocumentsDirectory() {
        let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: nil)
            
            for fileURL in contents where fileURL.pathExtension == "gguf" {
                try FileManager.default.removeItem(at: fileURL)
                print("🗑️ Deleted:", fileURL.lastPathComponent)
            }
            
        } catch {
            print("❌ Error deleting .gguf files:", error.localizedDescription)
        }
    }
    
    func updateSharedVideoCount() {
        let defaults = UserDefaults(suiteName: "group.com.codecraft.YouTube-Share")
        if let url = defaults?.url(forKey: "SharedVideoURL") {
            // Standardize the file URL to resolve any path issues
            let standardizedURL = url.standardizedFileURL
            sharedVideoCount = 1
            sharedVideoURL = standardizedURL
            showVideoPreview = true
        } else {
            sharedVideoCount = 0
        }
    }
    
}

