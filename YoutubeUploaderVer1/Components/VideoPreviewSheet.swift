//
//  SharedVideoPreviewSheet.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 23/05/25.
//

import SwiftUI
import AVKit

struct SharedVideoPreviewSheet: View {
    let videoURL: URL
    var onUploadTapped: () -> Void
    @Binding var isPresented: Bool

    var body: some View {
        VStack(spacing: 10) {
            VideoPlayer(player: AVPlayer(url: videoURL))
                .frame(minWidth: 400, minHeight: 300)
                .cornerRadius(12)

            HStack(spacing: 16) {
                Spacer()
                CustomButton(text: "Cancel", action: {
                    isPresented = false
                },backgroundColor: AppColor.grayText.color)
                CustomButton(text: "Upload", action: onUploadTapped)
            }
        }
        .padding()
    }
}


