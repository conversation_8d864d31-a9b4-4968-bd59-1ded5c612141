//
//  VideoSummarizationView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 02/06/25.
//

import SwiftUI

enum SummaryLength: String, CaseIterable, Identifiable {
    case brief = "Brief"
    case standard = "Standard"
    case detailed = "Detailed"
    
    var id: String { rawValue }
}

struct VideoSummarizationView: View {
    @ObservedObject var viewModel: VideoSummaryViewModel
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    
    @State private var showCopyAlert = false
    @State private var isTypewriterDone = false
    @State private var selectedSummaryLength: SummaryLength = .standard
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Summary Length")
                    .font(AppFontStyle.headline.style)
                
                HStack(spacing: 0) {
                    ForEach(SummaryLength.allCases) { option in
                        Button(action: {
                            selectedSummaryLength = option
                        }) {
                            Text(option.rawValue)
                                .font(AppFontStyle.body.style)
                                .foregroundColor(selectedSummaryLength == option ? .white : AppColor.primary.color)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 8)
                                .background(
                                    selectedSummaryLength == option
                                    ? AppColor.youtubeRed.color
                                    : Color.clear
                                )
                        }
                        .buttonStyle(.plain)
                    }
                }
                .background(AppColor.primary.color.opacity(0.1))
                .clipShape(RoundedRectangle(cornerRadius: 6))
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(AppColor.grayText.color.opacity(0.3), lineWidth: 1)
                )
                .frame(width:300)
                .padding(.vertical,10)
                Button(action: {
                    Task {
                        NotificationCenter.default.post(name: NSNotification.Name("VideoSummarizationStarted"), object: nil)
                        await viewModel.generateVideoSummary(from: transcriptItems, summaryLength: selectedSummaryLength)
                        NotificationCenter.default.post(name: NSNotification.Name("VideoSummarizationCompleted"), object: nil)
                    }
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: "text.alignleft")
                            .font(AppFontStyle.headline.style.weight(.medium))
                        Text("Summarize")
                            .font(AppFontStyle.headline.style)
                    }
                    .foregroundColor(AppColor.youtubeRed.color)
                    .padding(.vertical, 8)
                    .padding(.horizontal, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppColor.darkGrayBackground.color.opacity(0.3))
                    )
                }
                .buttonStyle(.plain)
                .padding(.bottom, 10)

            }
            
            HStack{
                Text("Generated Summary")
                    .font(AppFontStyle.headline.style)
                Spacer()
                if !viewModel.summary.isEmpty && !viewModel.isLoading && isTypewriterDone{
                    Button {
                        copySummaryToClipboard(summary: viewModel.summary)
                        showCopyAlert = true
                    } label: {
                        Image(systemName: "square.on.square")
                            .font(AppFontStyle.headline.style.weight(.medium))
                            .foregroundColor(.white)
                            .frame(width: 28, height: 28)
                            .background(AppColor.grayText.color.opacity(0.8))
                            .clipShape(Circle())
                    }
                    .buttonStyle(.plain)
                    .padding(8)
                    .help("Copy to clipboard")
                }
            }
            
            
            
            SummaryBoxView(isLoading: viewModel.isLoading, summary: viewModel.summary, isTypewriterDone: $isTypewriterDone)
            
            
            
            
        }
        .padding(24)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
        .alert("Summary Copied", isPresented: $showCopyAlert) {
            Button("OK", role: .cancel) { }
        }
        
    }
    
    private func copySummaryToClipboard(summary: String) {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(summary, forType: .string)
    }
}

struct SummaryBoxView: View {
    let isLoading: Bool
    let summary: String
    @Binding var isTypewriterDone: Bool
    
    @State private var displayedText = ""
    @State private var currentIndex = 0
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColor.darkBackground.color.opacity(isLoading || summary.isEmpty ? 0.2 : 0.5))
            
            HStack {
                if isLoading {
                    HStack(spacing: 12) {
                        Image(systemName: "doc.text.magnifyingglass")
                            .font(.title2)
                            .foregroundColor(.blue)
                            .symbolEffect(.variableColor.iterative, options: .repeating)
                        
                        Text("Analyzing content...")
                            .font(AppFontStyle.body.style)
                            .foregroundColor(AppColor.grayText.color)
                    }
                } else if !summary.isEmpty {
                    HStack(alignment: .top) {
                        Image(systemName: "quote.bubble.fill")
                            .font(.title2)
                            .foregroundColor(.blue)
                        
                        Text(displayedText)
                            .font(AppFontStyle.body.style)
                            .foregroundColor(AppColor.grayText.color)
                            .onAppear {
                                typewriterEffect()
                            }
                    }
                } else {
                    HStack {
                        Image(systemName: "plus.circle")
                            .font(.title2)
                            .foregroundColor(AppColor.grayText.color.opacity(0.7))
                        
                        Text("Click summarize to generate")
                            .font(AppFontStyle.body.style)
                            .foregroundColor(AppColor.grayText.color)
                    }
                }
                Spacer()
            }
            .padding()
        }
        .frame(maxWidth: .infinity)
    }
    
    private func typewriterEffect() {
        displayedText = ""
        currentIndex = 0
        isTypewriterDone = false
        
        Timer.scheduledTimer(withTimeInterval: 0.01, repeats: true) { timer in
            if currentIndex < summary.count {
                let index = summary.index(summary.startIndex, offsetBy: currentIndex)
                displayedText += String(summary[index])
                currentIndex += 1
            } else {
                timer.invalidate()
                isTypewriterDone = true
                
            }
        }
    }
}

