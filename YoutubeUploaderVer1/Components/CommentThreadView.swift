//
//  CommentThreadView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 29/04/25.
//

import SwiftUI

struct CommentThreadView: View {
    let commentThread: CommentThread
    @State private var showReplies = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            let comment = commentThread.snippet.topLevelComment.snippet
            
            CommentRowView(
                author: comment.authorDisplayName,
                content: comment.textDisplay,
                profileURL: URL(string: comment.authorChannelUrl),
                commentID: commentThread.id,
                isTopLevelComment: true,
                publishedAt: commentThread.snippet.topLevelComment.snippet.publishedAt
            )
            
//            if let replies = commentThread.replies?.comments, !replies.isEmpty {
//                VStack(alignment: .leading, spacing: 8) {
//                    ForEach(replies) { reply in
//                        CommentRowView(
//                            author: reply.snippet.authorDisplayName,
//                            content: reply.snippet.textDisplay,
//                            profileURL: URL(string: reply.snippet.authorChannelUrl),
//                            commentID: reply.id
//                        )
//                    }
//                }
//            }
            if let replies = commentThread.replies?.comments,!replies.isEmpty{
                VStack(alignment:.leading,spacing:8){
                    Button {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            showReplies.toggle()
                        }
                    } label:{
                        HStack(spacing: 8) {
                            // Vertical line for visual connection
                            Rectangle()
                                .fill(AppColor.grayText.color.opacity(0.4))
                                .frame(width: 2, height: 24)
                            
                            // Reply count and icon
                            HStack(spacing: 6) {
                                Image(systemName: showReplies ? "chevron.up" : "chevron.down")
                                    .font(AppFontStyle.body.style)
                                
                                Text("\(showReplies ? "Hide" : "Show") \(replies.count) \(replies.count == 1 ? "reply" : "replies")")
                                    .font(AppFontStyle.body.style)
                                    .fontWeight(.medium)
                            }
                            .foregroundColor(AppColor.primary.color.opacity(0.7))
                            .padding(.vertical, 6)
                            .padding(.horizontal, 10)
                            .background(
                                Capsule()
                                    .fill(AppColor.grayText.color.opacity(0.15))
                            )
                        }
                        .padding(.leading, 20)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.top, 4)
                    .padding(.bottom, showReplies ? 12 : 4)
                    
                    if showReplies{
                        VStack(alignment: .leading, spacing: 12){
                            ForEach(replies){ reply in
                                HStack(alignment: .top, spacing: 0){
                                    Rectangle()
                                        .fill(AppColor.grayText.color.opacity(0.4))
                                        .frame(width: 2)
                                        .padding(.leading, 20)
                                    CommentRowView(
                                        author: reply.snippet.authorDisplayName,
                                        content: reply.snippet.textDisplay,
                                        profileURL: URL(string:reply.snippet.authorChannelUrl),
                                        commentID: reply.id,
                                        isTopLevelComment: false, publishedAt: reply.snippet.publishedAt
                                    )
                                    .padding(.leading, 12)
                                }.padding(.leading,30)
                            }
                        }
                        .transition(.opacity.combined(with: .move(edge: .top)))
                    }
                }
            }

        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .frame(maxWidth: .infinity, alignment: .leading) 
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.darkGrayBackground.color)
        )
    }
}

//#Preview {
//    CommentThreadView()
//}
