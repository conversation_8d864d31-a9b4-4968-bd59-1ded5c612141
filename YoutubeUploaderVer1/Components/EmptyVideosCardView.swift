//
//  EmptyVideosCardView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashan<PERSON> B on 01/05/25.
//

import SwiftUI

struct EmptyVideosCardView: View {
    var title:String
    var description:String
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "film")
                .resizable()
                .scaledToFit()
                .frame(width: 60, height: 60)
                .foregroundColor(AppColor.primary.color)

            Text(title)
                .font(AppFontStyle.title2.style)
                .fontWeight(.semibold)
                .foregroundColor(AppColor.primary.color)

            Text(description)
                .font(AppFontStyle.body.style)
                .foregroundColor(AppColor.primary.color)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
        }
        .padding(30)
        .frame(maxWidth: 500)
        .background(AppColor.darkGrayBackground.color.opacity(0.8))
        .cornerRadius(20)
        .shadow(radius: 10)
    }
}



