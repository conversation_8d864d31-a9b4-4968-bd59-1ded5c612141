//
//  RecentActivityView.swift
//  YoutubeUploaderVer1
//
//  Created by Shashank B on 24/06/25.
//


import SwiftUI
import SwiftData
import AVKit
import UniformTypeIdentifiers
import AppKit

struct RecentActivityView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \RecentActivityItem.createdDate, order: .reverse) private var recentItems: [RecentActivityItem]
    @StateObject private var activityManager = RecentActivityManager()
    @State private var selectedContentType: ContentType? = nil
    @State private var showDeleteConfirmation = false
    @State private var itemToDelete: RecentActivityItem?
    
    var filteredItems: [RecentActivityItem] {
        if let selectedType = selectedContentType {
            return recentItems.filter { $0.contentType == selectedType }
        }
        return recentItems
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header Section
            headerSection
            
            // Content Filter Section
            filterSection
            
            // Main Content
            if filteredItems.isEmpty {
                emptyStateView
            } else {
                contentGridView
            }
        }
        .padding(24)
        .background(AppColor.surfacePrimary.color)
        .onAppear {
            activityManager.setModelContext(modelContext)
            activityManager.cleanupExpiredContent()
        }
        .alert("Delete Item", isPresented: $showDeleteConfirmation) {
            Button("Cancel", role: .cancel) {}
            Button("Delete", role: .destructive) {
                if let item = itemToDelete {
                    activityManager.deleteItem(item)
                }
            }
        } message: {
            Text("Are you sure you want to delete this item? This action cannot be undone.")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "clock.fill")
                    .font(AppFontStyle.title1.style)
                    .foregroundColor(AppColor.youtubeRed.color)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Recent Activity")
                        .font(AppFontStyle.largeTitle.style.weight(.bold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("Your generated content from the last 7 days")
                        .font(AppFontStyle.subheadline.style)
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                // Stats Badge
                HStack(spacing: 12) {
                    VStack(alignment: .center, spacing: 2) {
                        Text("\(recentItems.count)")
                            .font(AppFontStyle.title2.style.weight(.bold))
                            .foregroundColor(AppColor.youtubeRed.color)
                        Text("Total Items")
                            .font(AppFontStyle.caption2.style)
                            .foregroundColor(AppColor.textSecondary.color)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppColor.youtubeRed.color.opacity(0.1))
                    )
                }
            }
            
            Divider()
                .background(AppColor.borderPrimary.color.opacity(0.2))
                .padding(.top, 16)
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Filter Section
    private var filterSection: some View {
        HStack(spacing: 12) {
            Text("Filter:")
                .font(AppFontStyle.subheadline.style.weight(.medium))
                .foregroundColor(AppColor.textSecondary.color)
            
            // All Items Button
            Button(action: {
                selectedContentType = nil
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "square.grid.2x2")
                        .font(AppFontStyle.caption1.style)
                    Text("All")
                        .font(AppFontStyle.caption1.style.weight(.medium))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(selectedContentType == nil ? AppColor.youtubeRed.color : AppColor.surfaceSecondary.color)
                )
                .foregroundColor(selectedContentType == nil ? .white : AppColor.textSecondary.color)
            }
            .buttonStyle(PlainButtonStyle())
            
            // Content Type Filters
            ForEach(ContentType.allCases, id: \.self) { contentType in
                Button(action: {
                    selectedContentType = selectedContentType == contentType ? nil : contentType
                }) {
                    HStack(spacing: 6) {
                        Image(systemName: contentType.iconName)
                            .font(AppFontStyle.caption1.style)
                        Text(contentType.displayName)
                            .font(AppFontStyle.caption1.style.weight(.medium))
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(selectedContentType == contentType ? AppColor.youtubeRed.color : AppColor.surfaceSecondary.color)
                    )
                    .foregroundColor(selectedContentType == contentType ? .white : AppColor.textSecondary.color)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Spacer()
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 20) {
            Spacer()
            
            Image(systemName: "clock.badge.questionmark")
                .font(.system(size: 64))
                .foregroundColor(AppColor.textSecondary.color.opacity(0.5))
            
            VStack(spacing: 8) {
                Text("No Recent Activity")
                    .font(AppFontStyle.title2.style.weight(.semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Text("Generate videos or scripts to see them here.\nContent is automatically saved for 7 days.")
                    .font(AppFontStyle.body.style)
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Content Grid
    private var contentGridView: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16),
                GridItem(.flexible(), spacing: 16)
            ], spacing: 16) {
                ForEach(filteredItems) { item in
                    RecentActivityCard(
                        item: item,
                        onDelete: {
                            itemToDelete = item
                            showDeleteConfirmation = true
                        }
                    )
                }
            }
            .padding(.bottom, 20)
        }
    }
}

// MARK: - Recent Activity Card
struct RecentActivityCard: View {
    let item: RecentActivityItem
    let onDelete: () -> Void
    @State private var showPreview = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with type icon and actions
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: item.contentType.iconName)
                        .font(AppFontStyle.callout.style.weight(.medium))
                        .foregroundColor(AppColor.youtubeRed.color)
                    
                    Text(item.contentType.displayName)
                        .font(AppFontStyle.caption1.style.weight(.semibold))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                // Actions Menu
                Menu {
                    if item.contentType == .video, let _ = item.fileURL {
                        Button("Preview") {
                            showPreview = true
                        }
                    }
                    
                    
                    Button("Re-download") {
                        redownloadFile(item)
                    }
                    
                    Divider()
                    
                    Button("Delete", role: .destructive) {
                        onDelete()
                    }
                } label: {
                    Image(systemName: "line.3.horizontal")
                        .font(AppFontStyle.title3.style)
                        .foregroundColor(AppColor.textSecondary.color)
                        .padding(4)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            // Content Preview
            contentPreview
            
            // Metadata
            VStack(alignment: .leading, spacing: 6) {
                Text(item.title)
                    .font(AppFontStyle.subheadline.style.weight(.semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                    .lineLimit(2)
                
                HStack {
                    Text(item.formattedDate)
                        .font(AppFontStyle.caption2.style)
                        .foregroundColor(AppColor.textSecondary.color)
                    
                    Spacer()
                    
                    if item.daysUntilExpiration > 0 {
                        Text("\(item.daysUntilExpiration)d left")
                            .font(AppFontStyle.caption2.style)
                            .foregroundColor(AppColor.youtubeRed.color)
                    } else {
                        Text("Expires soon")
                            .font(AppFontStyle.caption2.style)
                            .foregroundColor(.orange)
                    }
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfaceSecondary.color)
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .sheet(isPresented: $showPreview) {
            if item.contentType == .video, let fileURL = item.fileURL {
                VideoPreviewSheet(videoURL: fileURL)
            }
        }
    }
    
    @ViewBuilder
    private var contentPreview: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(AppColor.darkGrayBackground.color.opacity(0.3))
            .frame(height: 120)
            .overlay {
                if item.contentType == .video {
                    if let thumbnailData = item.thumbnail,
                       let nsImage = NSImage(data: thumbnailData) {
                        Image(nsImage: nsImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .clipped()
                    } else {
                        VStack(spacing: 8) {
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 32))
                                .foregroundColor(AppColor.youtubeRed.color)
                            
                            if let duration = item.formattedDuration {
                                Text(duration)
                                    .font(AppFontStyle.caption2.style.weight(.medium))
                                    .foregroundColor(AppColor.textSecondary.color)
                            }
                        }
                    }
                } else {
                    VStack(spacing: 8) {
                        Image(systemName: "doc.text.fill")
                            .font(.system(size: 32))
                            .foregroundColor(AppColor.youtubeRed.color)
                        
                        if let wordCount = item.wordCount {
                            Text("\(wordCount) words")
                                .font(AppFontStyle.caption2.style.weight(.medium))
                                .foregroundColor(AppColor.textSecondary.color)
                        }
                    }
                }
            }
            .cornerRadius(8)
    }
    
    private func redownloadFile(_ item: RecentActivityItem) {
        guard let fileURL = item.fileURL else { return }
        
        let savePanel = NSSavePanel()
        
        if item.contentType == .video {
            savePanel.allowedContentTypes = [.mpeg4Movie, .quickTimeMovie, .movie]
        } else {
            savePanel.allowedContentTypes = [.plainText]
        }
        
        savePanel.nameFieldStringValue = fileURL.lastPathComponent
        savePanel.canCreateDirectories = true
        
        if savePanel.runModal() == .OK, let destinationURL = savePanel.url {
            do {
                try FileManager.default.copyItem(at: fileURL, to: destinationURL)
                NSWorkspace.shared.activateFileViewerSelecting([destinationURL])
            } catch {
                print("❌ Re-download failed: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Video Preview Sheet
struct VideoPreviewSheet: View {
    let videoURL: URL
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Video Preview")
                    .font(AppFontStyle.title2.style.weight(.bold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Spacer()
                
                Button("Close") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
            }
            .padding()
            
            VideoPlayer(player: AVPlayer(url: videoURL))
                .frame(height: 400)
                .cornerRadius(12)
                .padding(.horizontal)
            
            Spacer()
        }
        .background(AppColor.surfacePrimary.color)
    }
}
