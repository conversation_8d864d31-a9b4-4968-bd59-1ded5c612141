//
//  PlaylistCard.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 14/04/25.
//

import SwiftUI

struct PlaylistCard: View {
    let playlist: Playlist
    @State private var isHovered: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            
            // Thumbnail
            AsyncImage(url: URL(string: playlist.snippet.thumbnails.standard?.url ?? playlist.snippet.thumbnails.default.url)) { phase in
                switch phase {
                case .empty:
                    ZStack {
                        Color.gray.opacity(0.3)
                        ProgressView()
                    }
                    .frame(height: 180)

                case .success(let image):
                    image
                        .resizable()
                        .scaledToFit()
                        .frame(height: 180)
                        .frame(maxWidth: .infinity)
                        .clipped()

                case .failure:
                    ZStack {
                        LinearGradient(
                            gradient: Gradient(colors: [Color.red.opacity(0.6), Color.black]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                        Image(systemName: "film")
                            .font(AppFontStyle.largeTitle.style)
                            .fontWeight(.bold)
                            .foregroundColor(AppColor.primary.color.opacity(0.7))
                    }
                    .frame(height: 180)

                @unknown default:
                    EmptyView()
                }
            }
            .cornerRadius(12)

            
            // Info Section
            VStack(alignment: .leading, spacing: 6) {
                Text(playlist.snippet.title)
                    .font(AppFontStyle.title2.style)
                    .fontWeight(.semibold)
                    .foregroundColor(AppColor.primary.color)
                    .lineLimit(2)

                Text(playlist.snippet.description.isEmpty == false ? playlist.snippet.description : TextConstants.Playlists.NoDesc)
                    .font(AppFontStyle.subheadline.style)
                    .foregroundColor(AppColor.primary.color.opacity(0.85))
                    .lineLimit(2)
                    .truncationMode(.tail)


                Text("\(playlist.contentDetails.itemCount) \(playlist.contentDetails.itemCount == 1 ? "video" : "videos")")
                    .font(.footnote)
                    .foregroundColor(AppColor.youtubeRed.color.opacity(0.9))
                    .padding(.top, 2)
            }
            .padding(12)
            
            
            // Stats
            
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color)
                .shadow(
                    color: isHovered ? AppColor.youtubeRed.color.opacity(0.1) : Color.black.opacity(0.05),
                    radius: isHovered ? 12 : 8,
                    x: 0,
                    y: isHovered ? 6 : 2
                )
                .animation(.easeInOut(duration: 0.2), value: isHovered)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(
                    isHovered ? AppColor.youtubeRed.color.opacity(0.2) : AppColor.borderPrimary.color.opacity(0.1),
                    lineWidth: isHovered ? 2 : 1
                )
                .animation(.easeInOut(duration: 0.2), value: isHovered)
        )
        .onHover { hovering in
            isHovered = hovering
        }
    }
}


//#Preview {
//    PlaylistCard()
//}
