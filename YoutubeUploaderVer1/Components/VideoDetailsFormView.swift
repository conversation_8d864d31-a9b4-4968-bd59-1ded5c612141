//
//  VideoDetailsFormView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 04/05/25.
//

import SwiftUI

struct VideoDetailsFormView: View {
    @ObservedObject var viewModel: VideoUploadViewModel
    @State private var isGeneratingTitle: Bool = false
    @State private var isGeneratingDesc: Bool = false
    let showValidation: Bool
    let privacyOptions = ["Public", "Private", "Unlisted"]
    let localAIService = LocalAIService.shared
    let transcript:[(TimeInterval, TimeInterval, String)]
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            VStack(alignment: .leading, spacing: 20) {
                
                Text("Enter the title")
                    .font(AppFontStyle.headline.style)
                HStack{
                    
                    RoundedTextField(placeholder: "", text: $viewModel.title, isRequired: true,
                                     showValidation: showValidation)
                    Button {
                        Task{
                            await generateAITitle()
                        }
                    } label: {
                        HStack(spacing: 4) {
                            Image(systemName: "wand.and.stars")
                                .font(AppFontStyle.title3.style)
                                .foregroundColor(AppColor.primary.color)
                        }
                        .padding(8)
                       
                       
                    }
                    .buttonStyle(.plain)
                }
                
                Text("Enter the description")
                    .font(AppFontStyle.headline.style)
                HStack{
                    LargeRoundedTextField(placeholder: "", text: $viewModel.description, isRequired: true,
                                     showValidation: showValidation)
         
                    Button {
                        Task{
                            await generateAIDescription()
                        }
                    } label: {
                        HStack(spacing: 4) {
                            Image(systemName: "wand.and.stars")
                                .font(AppFontStyle.title3.style)
                                .foregroundColor(AppColor.primary.color)
                        }
                        .padding(8)
                        
                        
                    }
                    .buttonStyle(.plain)
                }
                
                
                // Privacy settings
                VStack(alignment: .leading, spacing: 8) {
                    Text("Privacy Settings")
                        .font(AppFontStyle.subheadline.style.weight(.medium))
                        .foregroundColor(AppColor.grayText.color)
                        .padding(.leading, 4)
                    
                    Picker("Privacy", selection: $viewModel.privacyStatus) {
                        ForEach(privacyOptions, id: \.self) { option in
                            Text(option)
                        }
                    }
                    .labelsHidden()
                    .pickerStyle(SegmentedPickerStyle())
                    .controlSize(.large)
                }
                
                // Additional settings
                VStack(alignment: .leading, spacing: 16) {
                    Divider()
                        .padding(.vertical, 8)
                    
                    // Made for Kids toggle
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Made for Kids")
                                .font(AppFontStyle.subheadline.style.weight(.medium))
                                .foregroundColor(AppColor.primary.color)
                            
                            Text("Designate whether this content is made for kids")
                                .font(AppFontStyle.caption1.style)
                                .foregroundColor(AppColor.grayText.color)
                        }
                        
                        Spacer()
                        
                        Toggle("", isOn: $viewModel.madeForKids)
                            .labelsHidden()
                            .toggleStyle(SwitchToggleStyle(tint: AppColor.youtubeRed.color))
                            .scaleEffect(0.8)
                    }
                    
                    // Notify Subscribers toggle
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Notify Subscribers")
                                .font(AppFontStyle.subheadline.style.weight(.medium))
                                .foregroundColor(AppColor.primary.color)
                            
                            Text("Send notification to your subscribers about this upload")
                                .font(AppFontStyle.caption1.style)
                                .foregroundColor(AppColor.grayText.color)
                        }
                        
                        Spacer()
                        
                        Toggle("", isOn: $viewModel.notifySubscribers)
                            .labelsHidden()
                            .toggleStyle(SwitchToggleStyle(tint: AppColor.youtubeRed.color))
                            .scaleEffect(0.8)
                    }
                }
                .padding(.top, 8)
                .padding(.horizontal, 4)
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(AppColor.darkGrayBackground.color.opacity(0.5))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 24)
                .stroke(AppColor.grayText.color.opacity(0.1), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.1), radius: 8, x: 0, y: 4)
        .frame(maxWidth: .infinity)
        .overlay(
            Group {
                if isGeneratingTitle || isGeneratingDesc {
                    VStack {
                        ProgressView()
                            .scaleEffect(1.0)
                        Text("Generating...")
                            .font(AppFontStyle.footnote.style)
                            .foregroundColor(AppColor.grayText.color)
                            .padding(.top, 8)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(AppColor.darkBackground.color)
                            .shadow(color: Color.black.opacity(0.2), radius: 10)
                    )
                }
            }
        )
    }
    
    private func  generateAITitle() async{
        guard !transcript.isEmpty else {
            return
        }
        isGeneratingTitle = true
       let prompt = """
        Based on the following transcript, generate a YouTube video title only. Return just the title text — no explanation, no extra text. 
        The title should be: 5–10 words, under 60 characters, compelling, SEO-friendly, and curiosity-driven. Do not include anything else.
        If the transcript is empty provide a message saying you cant produce title with empty script
        
        TRANSCRIPT:
        \(transcript)
        
        """
        await localAIService.send(prompt: prompt)
        DispatchQueue.main.async {
            let generatedTitle = localAIService.response
            
            // Update the viewModel title
            if !generatedTitle.isEmpty {
                viewModel.title = generatedTitle
            }
            
            isGeneratingTitle = false
        }
        
    }
    private func  generateAIDescription() async{
        guard !transcript.isEmpty else {
            return
        }
        isGeneratingDesc = true
       let prompt = """
        Based on the following transcript, generate a compelling and concise YouTube video description (2-3 sentences) that summarizes the video content, 
        highlights its value, and includes relevant keywords for search optimization. Return only the video description text.
        If the transcript is empty provide a message saying you cant produce descritpion with empty script
        
        TRANSCRIPT:
        \(transcript)
        
        """
        await localAIService.send(prompt: prompt)
        DispatchQueue.main.async {
            let generatedDesc = localAIService.response
            
            // Update the viewModel title
            if !generatedDesc.isEmpty {
                viewModel.description = generatedDesc
            }
            
            isGeneratingDesc = false
        }

    }
}

