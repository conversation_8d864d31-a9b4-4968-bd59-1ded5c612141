//
//  YoutubeUploaderVer1App.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 03/04/25.
//

import SwiftUI
import Cocoa
import PythonKit
import SwiftData

@main
struct YoutubeUploaderVer1App: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject var googleSignInHelper = GoogleSignInHelper()
    @StateObject var videoAnalyticsViewModel = YouTubeVideoAnalyticsViewModel(googleSignInHelper: GoogleSignInHelper())
    @AppStorage("themePreference") var storedThemePreference: ThemePreference = .system
    @StateObject var startupManager = StartupManager()
    @StateObject var sharedVideoHandler = SharedVideoHandler()
    @StateObject var recentActivityManager = RecentActivityManager()

    init() {
        initializePythonWithPythonKit()
    }

    // SwiftData configuration
    var sharedModelContainer: ModelContainer = {
        let schema = Schema([
            RecentActivityItem.self
        ])
        let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)

        do {
            return try ModelContainer(for: schema, configurations: [modelConfiguration])
        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }()
    
    
    var body: some Scene {
        WindowGroup {
            RootView()
                .environmentObject(googleSignInHelper)
                .environmentObject(videoAnalyticsViewModel)
                .environmentObject(sharedVideoHandler)
                .environmentObject(startupManager)
                .environmentObject(recentActivityManager)
                .preferredColorScheme(storedThemePreference.colorScheme)
                .onAppear {
                    // Set up Recent Activity manager with model context
                    recentActivityManager.setModelContext(sharedModelContainer.mainContext)

                    // Clean up expired content on app launch
                    recentActivityManager.cleanupExpiredContent()

                    // Run memory management demo in debug mode (disabled by default)
                    #if DEBUG && false // Change to true to enable demo
                    DispatchQueue.global(qos: .background).async {
                        MemoryManagementDemo.runDemo()
                        SystemSpecsTest.runTests()
                    }
                    #endif

                    sharedVideoHandler.checkForSharedVideo()
                }
        }
        .modelContainer(sharedModelContainer)
    }
    
    private func initializePythonWithPythonKit() {
        var determinedPythonHome: String?
        let bundlePath = Bundle.main.bundlePath
        let calculatedPythonHome = "\(bundlePath)/Contents/Frameworks/Python.framework/Versions/3.13"
        determinedPythonHome = calculatedPythonHome
        
        if let pythonHome = determinedPythonHome {
            let _ = "\(pythonHome)/lib/python3.13"
        } else {
            return
        }
        let sys =  Python.import("sys")
        sys.path.append("\(calculatedPythonHome)/lib/python3.13/site-packages")
        let _ =  Python.import("os")
        let _ = Python.import("google.genai")

    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    func application(_ application: NSApplication, open urls: [URL]) {
        for url in urls {
            print("Opened with URL: \(url)")
        }
    }

    func applicationDidFinishLaunching(_ notification: Notification) {
        // Add keyboard shortcut for sidebar toggle (Cmd+Shift+S)
        let mainMenu = NSApplication.shared.mainMenu
        if let viewMenu = mainMenu?.item(withTitle: "View")?.submenu {
            let toggleSidebarItem = NSMenuItem(
                title: "Toggle Sidebar",
                action: #selector(toggleSidebar),
                keyEquivalent: "s"
            )
            toggleSidebarItem.keyEquivalentModifierMask = [.command, .shift]
            toggleSidebarItem.target = self
            viewMenu.addItem(toggleSidebarItem)
        }
    }

    @objc func toggleSidebar() {
        NotificationCenter.default.post(name: NSNotification.Name("ToggleSidebar"), object: nil)
    }
}

